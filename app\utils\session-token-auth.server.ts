/**
 * Session Token Authentication Handler
 * Properly handles Shopify's session token authentication for embedded apps
 */

import { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export interface SessionTokenAuth {
  admin: any;
  session: {
    id: string;
    shop: string;
    accessToken: string;
    expires?: Date;
    userId?: string;
    email?: string;
    isOnline: boolean;
    scope?: string;
  };
}

/**
 * Session token authentication that properly handles Shopify's auth flow
 */
export async function authenticateSessionToken(
  request: Request
): Promise<SessionTokenAuth> {
  const url = new URL(request.url);
  const pathname = url.pathname;

  try {
    console.log(`🔐 [SESSION-TOKEN] Authenticating request: ${pathname}`);

    // Use Shopify's authenticate.admin which handles session tokens automatically
    const result = await authenticate.admin(request);

    console.log(`✅ [SESSION-TOKEN] Authentication successful for shop: ${result.session.shop}`);

    // Validate the result
    if (!result.session?.shop || !result.session?.accessToken) {
      throw new Error('Invalid session data received from Shopify');
    }

    if (!result.admin) {
      throw new Error('Admin client not available');
    }

    return result;
  } catch (error: any) {
    console.error(`❌ [SESSION-TOKEN] Authentication failed:`, {
      message: error.message,
      status: error.status,
      isResponse: error instanceof Response,
      url: request.url
    });

    // If it's a Response (redirect from Shopify), let it through
    // This is normal behavior for session token authentication
    if (error instanceof Response) {
      console.log(`🔄 [SESSION-TOKEN] Shopify redirect response, passing through for: ${pathname}`);
      throw error;
    }

    // For other errors, also let Shopify handle them
    throw error;
  }
}

/**
 * Session token wrapper for loaders
 */
export function withSessionToken<T>(
  loaderFn: (args: LoaderFunctionArgs & { auth: SessionTokenAuth }) => Promise<T>
) {
  return async (args: LoaderFunctionArgs): Promise<T> => {
    const auth = await authenticateSessionToken(args.request);
    return await loaderFn({ ...args, auth });
  };
}

/**
 * Session token wrapper for actions
 */
export function withSessionTokenAction<T>(
  actionFn: (args: ActionFunctionArgs & { auth: SessionTokenAuth }) => Promise<T>
) {
  return async (args: ActionFunctionArgs): Promise<T> => {
    const auth = await authenticateSessionToken(args.request);
    return await actionFn({ ...args, auth });
  };
}
