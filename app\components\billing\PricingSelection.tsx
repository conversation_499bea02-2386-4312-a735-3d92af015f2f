import * as React from "react";
import { useFetcher, useNavigate } from "@remix-run/react";
import { motion } from "framer-motion";
import { Button as UIButton } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Check, Star, Zap, Clock, DollarSign } from "lucide-react";
import { BillingPlan } from "../../services/billing.server";
import { getBillingErrorMessage, getBillingSuccessMessage } from "../../utils/user-messages";

import { useAppBridge } from "@shopify/app-bridge-react";

interface PricingSelectionProps {
  plans: BillingPlan[];
  onPlanSelect?: (planId: string) => void;
  selectedPlan?: string;
  showPayPerUse?: boolean;
  csrfToken?: string;
  hasActiveSubscription?: boolean;
}

export function PricingSelection({
  plans,
  onPlanSelect,
  selectedPlan,
  showPayPerUse = true,
  csrfToken,
  hasActiveSubscription = false
}: PricingSelectionProps) {
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const shopify = useAppBridge();
  const [localSelectedPlan, setLocalSelectedPlan] = React.useState(selectedPlan || 'annual');

  // Handle fetcher errors and state changes with improved user experience
  React.useEffect(() => {
    console.log('🔄 Fetcher state:', fetcher.state);
    console.log('📋 Fetcher data:', fetcher.data);

    if (fetcher.data && typeof fetcher.data === 'object') {
      const data = fetcher.data as any;

      if (data.error) {
        console.error('❌ Billing error:', data.error);

        // Get user-friendly error message
        const userMessage = getBillingErrorMessage(data.error);

        // Show user-friendly error with action button
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = `
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">⚠️</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${userMessage.title}</h4>
                <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">${userMessage.message}</p>
                ${userMessage.action ? `
                  <button onclick="${userMessage.action.onClick.toString()}()" style="
                    background: #dc2626; color: white; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">${userMessage.action.label}</button>
                ` : ''}
              </div>
              <button onclick="this.parentElement.parentElement.remove()" style="
                background: none; border: none; color: #991b1b; cursor: pointer; font-size: 18px;
              ">×</button>
            </div>
          </div>
        `;
        document.body.appendChild(errorDiv);

        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (errorDiv.parentElement) {
            errorDiv.remove();
          }
        }, 10000);

      } else if (data.success && data.confirmationUrl) {
        console.log('✅ Subscription created successfully, redirecting to confirmation...');
        console.log('🔗 Confirmation URL:', data.confirmationUrl.substring(0, 100) + '...');

        // Show success message before redirect
        const successMessage = getBillingSuccessMessage('subscription_created', {
          planName: 'subscription'
        });

        const successDiv = document.createElement('div');
        successDiv.innerHTML = `
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">✅</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${successMessage.title}</h4>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Redirecting to payment...</p>
              </div>
            </div>
          </div>
        `;
        document.body.appendChild(successDiv);

        try {
          // Use window.top to break out of iframe context
          setTimeout(() => {
            if (window.top) {
              window.top.location.href = data.confirmationUrl;
            } else {
              window.location.href = data.confirmationUrl;
            }
          }, 1500); // Small delay to show success message
        } catch (error) {
          console.error('❌ Failed to redirect to confirmation URL:', error);
          successDiv.remove();

          const redirectErrorDiv = document.createElement('div');
          redirectErrorDiv.innerHTML = `
            <div style="
              position: fixed; top: 20px; right: 20px; z-index: 1000;
              background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
              padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            ">
              <div style="display: flex; align-items: start; gap: 12px;">
                <div style="color: #ffffff; font-size: 20px;">⚠️</div>
                <div style="flex: 1;">
                  <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">Redirect Issue</h4>
                  <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Subscription created successfully, but failed to redirect. Please refresh the page.</p>
                  <button onclick="window.location.reload()" style="
                    background: #ffffff; color: #000000; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">Refresh Page</button>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                  background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 18px;
                ">×</button>
              </div>
            </div>
          `;
          document.body.appendChild(redirectErrorDiv);
        }
      }
    }

    if (fetcher.state === 'loading') {
      console.log('⏳ Billing request in progress...');
    }

    if (fetcher.state === 'idle' && fetcher.data) {
      console.log('✅ Billing request completed');
    }
  }, [fetcher.data, fetcher.state]);

  const handlePlanSelect = (planId: string) => {
    setLocalSelectedPlan(planId);
    onPlanSelect?.(planId);
  };

  const handleStartTrial = (planId: string) => {
    console.log('🔄 Starting trial for plan:', planId);
    console.log('🔐 CSRF token available:', !!csrfToken);

    // Validate plan ID
    if (!planId || typeof planId !== 'string') {
      console.error('❌ Invalid plan ID:', planId);
      alert('Error: Invalid plan selected');
      return;
    }

    // Check if CSRF token is available
    if (!csrfToken) {
      console.error('❌ No CSRF token available');
      alert('Security token missing. Please refresh the page and try again.');
      return;
    }

    // Check if already processing
    if (fetcher.state === 'submitting' || fetcher.state === 'loading') {
      console.log('⏳ Already processing subscription request...');
      return;
    }

    // Create form data with proper encoding
    const formData = new FormData();
    formData.append('action', 'create_subscription');
    formData.append('planId', planId);
    if (csrfToken) {
      formData.append('csrfToken', csrfToken);
    }

    console.log('📤 Submitting billing form with data:', {
      action: 'create_subscription',
      planId: planId,
      hasCSRF: !!csrfToken
    });

    // Submit to current page (will work on both /app/billing and /app/billing/pricing)
    fetcher.submit(formData, {
      method: 'POST'
    });
  };

  const getPlanIcon = (planType: string) => {
    switch (planType) {
      case 'annual':
        return <Star className="w-6 h-6 text-yellow-500" />;
      case 'monthly':
        return <Clock className="w-6 h-6 text-blue-500" />;
      case 'pay_per_use':
        return <Zap className="w-6 h-6 text-green-500" />;
      default:
        return <DollarSign className="w-6 h-6 text-gray-500" />;
    }
  };

  const calculateMonthlySavings = () => {
    const annualPlan = plans.find(p => p.id === 'annual');
    const monthlyPlan = plans.find(p => p.id === 'monthly');
    if (annualPlan && monthlyPlan) {
      const annualMonthly = annualPlan.price / 12;
      const savings = (monthlyPlan.price - annualMonthly) * 12;
      return Math.round(savings);
    }
    return 0;
  };

  // Hide pay-per-use when user has active subscription, or when showPayPerUse is false
  const shouldShowPayPerUse = showPayPerUse && !hasActiveSubscription;
  const filteredPlans = shouldShowPayPerUse ? plans : plans.filter(p => p.type !== 'pay_per_use');

  console.log('🔍 PricingSelection - showPayPerUse:', showPayPerUse);
  console.log('🔍 PricingSelection - hasActiveSubscription:', hasActiveSubscription);
  console.log('🔍 PricingSelection - shouldShowPayPerUse:', shouldShowPayPerUse);
  console.log('🔍 PricingSelection - filteredPlans:', filteredPlans.map(p => p.id));

  return (
    <div className="max-w-6xl mx-auto p-6 bg-black text-white">
      {/* Header */}
      <div className="text-center mb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold mb-4 text-white">Choose Your Plan</h1>
          <p className="text-xl text-white/70 mb-6">
            Start optimizing your products with our powerful SEO tools
          </p>
        </motion.div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        {filteredPlans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <Card
              className={`relative h-full transition-all duration-300 hover:shadow-xl cursor-pointer bg-white/10 border-white/20 ${
                plan.recommended
                  ? 'border-2 border-white shadow-lg scale-105'
                  : localSelectedPlan === plan.id
                    ? 'border-2 border-blue-400'
                    : 'border-white/20 hover:border-white/40'
              }`}
              onClick={() => handlePlanSelect(plan.id)}
            >
              {plan.recommended && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-white text-black px-4 py-1 text-sm font-semibold">
                    <Star className="w-4 h-4 mr-1" />
                    Best Value
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  {getPlanIcon(plan.type)}
                </div>
                <CardTitle className="text-2xl font-bold text-white">{plan.name}</CardTitle>
                <CardDescription className="text-base text-white/70">{plan.description}</CardDescription>
                
                <div className="mt-4">
                  {plan.type === 'pay_per_use' ? (
                    <div>
                      <span className="text-4xl font-bold text-white">${plan.price}</span>
                      <span className="text-white/70 ml-2">per product</span>
                    </div>
                  ) : (
                    <div>
                      <span className="text-4xl font-bold text-white">${plan.price}</span>
                      <span className="text-white/70 ml-2">
                        /{plan.type === 'annual' ? 'year' : 'month'}
                      </span>
                      {plan.type === 'annual' && (
                        <div className="text-sm text-green-400 font-medium mt-1">
                          Save ${calculateMonthlySavings()}/year vs monthly
                        </div>
                      )}
                      {plan.type === 'monthly' && (
                        <div className="text-sm text-white/70 mt-1">
                          Equivalent to ${(plan.price).toFixed(2)}/month
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-white/80">{feature}</span>
                    </li>
                  ))}
                </ul>

                {plan.type !== 'pay_per_use' ? (
                  <UIButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleStartTrial(plan.id);
                    }}
                    className={`w-full ${
                      plan.recommended
                        ? 'bg-white text-black hover:bg-white/90 disabled:bg-white/60'
                        : 'bg-white/20 text-white hover:bg-white/30 disabled:bg-white/10'
                    } disabled:cursor-not-allowed transition-all duration-200`}
                    disabled={fetcher.state === 'submitting' || fetcher.state === 'loading'}
                  >
                    {fetcher.state === 'submitting' || fetcher.state === 'loading' ? (
                      <>
                        <Clock className="w-4 h-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Subscribe Now
                      </>
                    )}
                  </UIButton>
                ) : (
                  <UIButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handlePlanSelect(plan.id);
                      console.log('🎯 Pay-per-use selected, navigating to SEO dashboard');

                      // Show user feedback
                      shopify.toast.show('Pay-per-use selected! Select products to optimize and pay only for what you use.', {
                        isError: false
                      });

                      // Navigate to SEO dashboard where users can select products for optimization
                      navigate('/app/seo-dashboard');
                    }}
                    className="w-full border-2 border-white/30 bg-transparent text-white hover:bg-white hover:text-black"
                  >
                    Select Pay-Per-Use
                  </UIButton>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Comparison Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white/10 border border-white/20 rounded-3xl p-8"
      >
        <h3 className="text-2xl font-bold text-center mb-8 text-white">Plan Comparison</h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-4 px-4">Feature</th>
                <th className="text-center py-4 px-4">Annual</th>
                <th className="text-center py-4 px-4">Monthly</th>
                <th className="text-center py-4 px-4">Pay-Per-Use</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              <tr className="border-b">
                <td className="py-4 px-4 font-medium">Product Optimizations</td>
                <td className="text-center py-4 px-4">Unlimited</td>
                <td className="text-center py-4 px-4">Unlimited</td>
                <td className="text-center py-4 px-4">$0.10 each</td>
              </tr>
              <tr className="border-b">
                <td className="py-4 px-4 font-medium">Support Level</td>
                <td className="text-center py-4 px-4">Priority</td>
                <td className="text-center py-4 px-4">Standard</td>
                <td className="text-center py-4 px-4">Standard</td>
              </tr>
              <tr className="border-b">
                <td className="py-4 px-4 font-medium">Analytics</td>
                <td className="text-center py-4 px-4">Advanced</td>
                <td className="text-center py-4 px-4">Basic</td>
                <td className="text-center py-4 px-4">Basic</td>
              </tr>
              <tr>
                <td className="py-4 px-4 font-medium">Monthly Commitment</td>
                <td className="text-center py-4 px-4">Annual only</td>
                <td className="text-center py-4 px-4">Yes</td>
                <td className="text-center py-4 px-4">None</td>
              </tr>
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="mt-12 text-center"
      >
        <h3 className="text-xl font-semibold mb-4">Frequently Asked Questions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto">
          <div>
            <h4 className="font-medium mb-2">Can I change plans later?</h4>
            <p className="text-sm text-muted-foreground">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">How does billing work?</h4>
            <p className="text-sm text-muted-foreground">
              You'll be charged immediately upon subscription. Monthly plans bill every 30 days, annual plans bill yearly.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">Is there a setup fee?</h4>
            <p className="text-sm text-muted-foreground">
              No setup fees. You only pay the subscription price or per-use charges.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">Can I cancel anytime?</h4>
            <p className="text-sm text-muted-foreground">
              Yes, you can cancel your subscription at any time. No long-term contracts required.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
