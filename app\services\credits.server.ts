import db from "../db.server";

export interface CreditBalance {
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  lastUpdated: Date;
}

export interface CreditTransaction {
  id: number;
  shop: string;
  type: 'PURCHASE' | 'USAGE' | 'REFUND' | 'BONUS';
  amount: number;
  description: string;
  referenceId?: string;
  createdAt: Date;
}

export class CreditsService {
  private shop: string;

  constructor(shop: string) {
    this.shop = shop;
  }

  private convertToNumber(value: any): number {
    if (value === null || value === undefined) return 0;
    if (typeof value === 'bigint') return Number(value);
    if (typeof value === 'number') return value;
    if (typeof value === 'string') return parseFloat(value) || 0;
    return 0;
  }

  private convertToString(value: any): string | undefined {
    if (value === null || value === undefined) return undefined;
    return String(value);
  }

  private convertToDate(value: any): Date {
    if (value === null || value === undefined) return new Date();
    if (value instanceof Date) return value;
    if (typeof value === 'string') return new Date(value);
    return new Date();
  }

  /**
   * Get current credit balance for the shop
   */
  async getCreditBalance(): Promise<CreditBalance> {
    try {
      // Get all credit transactions for this shop
      const transactions = await db.billingUsage.findMany({
        where: { shop: this.shop },
        orderBy: { optimizationDate: 'desc' }
      });

      // Calculate total credits purchased (include both ACCEPTED and PENDING for immediate display)
      const purchases = await db.billingPurchase.findMany({
        where: {
          shop: this.shop,
          status: { in: ['ACCEPTED', 'PENDING'] } // Include pending purchases for immediate credit display
        }
      });

      // Calculate total credits from purchases (each $0.10 = 1 credit)
      const totalCredits = purchases.reduce((sum, purchase) => {
        const count = this.convertToNumber(purchase.productCount);
        return sum + count;
      }, 0);

      // Calculate used credits
      const usedCredits = transactions.reduce((sum, usage) => {
        const optimized = this.convertToNumber(usage.productsOptimized);
        return sum + optimized;
      }, 0);

      const remainingCredits = Math.max(0, totalCredits - usedCredits);

      return {
        totalCredits,
        usedCredits,
        remainingCredits,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Error getting credit balance:', error);
      return {
        totalCredits: 0,
        usedCredits: 0,
        remainingCredits: 0,
        lastUpdated: new Date()
      };
    }
  }

  /**
   * Add credits when a purchase is made
   */
  async addCredits(amount: number, purchaseId: string, _description: string): Promise<void> {
    try {
      console.log(`💳 Adding ${amount} credits for shop ${this.shop} from purchase ${purchaseId}`);
      
      // Credits are automatically tracked through the purchase record
      // No need for separate credit transactions as we calculate from purchases
      
      console.log(`✅ Credits added successfully for shop ${this.shop}`);
    } catch (error) {
      console.error('Error adding credits:', error);
      throw error;
    }
  }

  /**
   * Use credits for optimization
   */
  async useCredits(amount: number, _description: string, batchId?: string): Promise<boolean> {
    try {
      const balance = await this.getCreditBalance();
      
      if (balance.remainingCredits < amount) {
        console.log(`❌ Insufficient credits for shop ${this.shop}. Required: ${amount}, Available: ${balance.remainingCredits}`);
        return false;
      }

      // Record the usage
      await db.billingUsage.create({
        data: {
          shop: this.shop,
          billingType: 'pay_per_use',
          productsOptimized: amount,
          optimizationDate: new Date(),
          batchId: batchId || `batch_${Date.now()}`
        }
      });

      console.log(`✅ Used ${amount} credits for shop ${this.shop}. Remaining: ${balance.remainingCredits - amount}`);
      return true;
    } catch (error) {
      console.error('Error using credits:', error);
      throw error;
    }
  }

  /**
   * Check if shop has enough credits for optimization
   */
  async hasEnoughCredits(requiredAmount: number): Promise<boolean> {
    try {
      const balance = await this.getCreditBalance();
      return balance.remainingCredits >= requiredAmount;
    } catch (error) {
      console.error('Error checking credit balance:', error);
      return false;
    }
  }

  /**
   * Get credit transaction history
   */
  async getCreditHistory(): Promise<Array<{
    type: string;
    amount: number;
    description: string;
    date: Date;
    referenceId?: string;
  }>> {
    try {
      const history: Array<{
        type: string;
        amount: number;
        description: string;
        date: Date;
        referenceId?: string;
      }> = [];

      // Get purchases (credit additions)
      const purchases = await db.billingPurchase.findMany({
        where: { shop: this.shop },
        orderBy: { createdAt: 'desc' }
      });

      purchases.forEach(purchase => {
        const statusText = purchase.status === 'PENDING' ? ' (Processing)' : '';
        const amount = this.convertToNumber(purchase.productCount);
        history.push({
          type: 'PURCHASE',
          amount: amount,
          description: `Purchased ${amount} optimization credits${statusText}`,
          date: this.convertToDate(purchase.createdAt),
          referenceId: this.convertToString(purchase.purchaseId)
        });
      });

      // Get usage (credit deductions)
      const usage = await db.billingUsage.findMany({
        where: { shop: this.shop },
        orderBy: { optimizationDate: 'desc' }
      });

      usage.forEach(use => {
        const optimized = this.convertToNumber(use.productsOptimized);
        history.push({
          type: 'USAGE',
          amount: -optimized,
          description: `Optimized ${optimized} products`,
          date: this.convertToDate(use.optimizationDate),
          referenceId: this.convertToString(use.batchId) || undefined
        });
      });

      // Sort by date (newest first)
      return history.sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error('Error getting credit history:', error);
      return [];
    }
  }

  /**
   * Get subscription status and unlimited access
   */
  async hasUnlimitedAccess(): Promise<boolean> {
    try {
      const activeSubscription = await db.billingSubscription.findFirst({
        where: {
          shop: this.shop,
          status: 'ACTIVE'
        }
      });

      return !!activeSubscription;
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  /**
   * Check if user can optimize products (either has subscription or credits)
   */
  async canOptimizeProducts(productCount: number): Promise<{
    canOptimize: boolean;
    reason?: string;
    hasSubscription?: boolean;
    creditsAvailable?: number;
  }> {
    try {
      // Check for active subscription first
      const hasSubscription = await this.hasUnlimitedAccess();
      if (hasSubscription) {
        return {
          canOptimize: true,
          hasSubscription: true
        };
      }

      // Check credits
      const balance = await this.getCreditBalance();
      const hasEnoughCredits = balance.remainingCredits >= productCount;

      return {
        canOptimize: hasEnoughCredits,
        hasSubscription: false,
        creditsAvailable: balance.remainingCredits,
        reason: hasEnoughCredits ? undefined : `Insufficient credits. You need ${productCount} credits but only have ${balance.remainingCredits}.`
      };
    } catch (error) {
      console.error('Error checking optimization access:', error);
      return {
        canOptimize: false,
        reason: 'Error checking access permissions'
      };
    }
  }
}
