-- CreateTable
CREATE TABLE "public"."Session" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "scope" TEXT,
    "expires" TIMESTAMP(3),
    "accessToken" TEXT NOT NULL,
    "userId" TEXT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "accountOwner" BOOLEAN NOT NULL DEFAULT false,
    "locale" TEXT,
    "collaborator" BOOLEAN DEFAULT false,
    "emailVerified" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "subscriptionId" TEXT,
    "subscriptionStatus" TEXT,
    "billingPlanId" TEXT,
    "trialEndsAt" TIMESTAMP(3),
    "lastBillingCheck" TIMESTAMP(3),

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BillingSubscription" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "planId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "trialDays" INTEGER NOT NULL DEFAULT 0,
    "trialEndsAt" TIMESTAMP(3),
    "currentPeriodStart" TIMESTAMP(3),
    "currentPeriodEnd" TIMESTAMP(3),
    "priceAmount" DOUBLE PRECISION,
    "priceCurrency" TEXT NOT NULL DEFAULT 'USD',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BillingSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BillingPurchase" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "purchaseId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "productCount" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "description" TEXT,
    "optimizationBatchId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BillingPurchase_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BillingUsage" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "billingType" TEXT NOT NULL,
    "billingReferenceId" TEXT,
    "productsOptimized" INTEGER NOT NULL,
    "optimizationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "batchId" TEXT,

    CONSTRAINT "BillingUsage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BillingEvent" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "eventType" TEXT NOT NULL,
    "referenceId" TEXT,
    "eventData" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BillingEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CSRFToken" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CSRFToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."RateLimitEntry" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "resetTime" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RateLimitEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CacheEntry" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "data" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "tags" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CacheEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Session_shop_key" ON "public"."Session"("shop");

-- CreateIndex
CREATE INDEX "Session_shop_idx" ON "public"."Session"("shop");

-- CreateIndex
CREATE INDEX "Session_expires_idx" ON "public"."Session"("expires");

-- CreateIndex
CREATE UNIQUE INDEX "BillingSubscription_subscriptionId_key" ON "public"."BillingSubscription"("subscriptionId");

-- CreateIndex
CREATE INDEX "BillingSubscription_shop_idx" ON "public"."BillingSubscription"("shop");

-- CreateIndex
CREATE INDEX "BillingSubscription_status_idx" ON "public"."BillingSubscription"("status");

-- CreateIndex
CREATE UNIQUE INDEX "BillingPurchase_purchaseId_key" ON "public"."BillingPurchase"("purchaseId");

-- CreateIndex
CREATE INDEX "BillingPurchase_shop_idx" ON "public"."BillingPurchase"("shop");

-- CreateIndex
CREATE INDEX "BillingPurchase_status_idx" ON "public"."BillingPurchase"("status");

-- CreateIndex
CREATE INDEX "BillingUsage_shop_idx" ON "public"."BillingUsage"("shop");

-- CreateIndex
CREATE INDEX "BillingUsage_optimizationDate_idx" ON "public"."BillingUsage"("optimizationDate");

-- CreateIndex
CREATE INDEX "BillingEvent_shop_idx" ON "public"."BillingEvent"("shop");

-- CreateIndex
CREATE INDEX "BillingEvent_eventType_idx" ON "public"."BillingEvent"("eventType");

-- CreateIndex
CREATE UNIQUE INDEX "CSRFToken_token_key" ON "public"."CSRFToken"("token");

-- CreateIndex
CREATE INDEX "CSRFToken_shop_idx" ON "public"."CSRFToken"("shop");

-- CreateIndex
CREATE INDEX "CSRFToken_expires_idx" ON "public"."CSRFToken"("expires");

-- CreateIndex
CREATE INDEX "CSRFToken_token_idx" ON "public"."CSRFToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "RateLimitEntry_identifier_key" ON "public"."RateLimitEntry"("identifier");

-- CreateIndex
CREATE INDEX "RateLimitEntry_identifier_idx" ON "public"."RateLimitEntry"("identifier");

-- CreateIndex
CREATE INDEX "RateLimitEntry_resetTime_idx" ON "public"."RateLimitEntry"("resetTime");

-- CreateIndex
CREATE UNIQUE INDEX "CacheEntry_key_key" ON "public"."CacheEntry"("key");

-- CreateIndex
CREATE INDEX "CacheEntry_key_idx" ON "public"."CacheEntry"("key");

-- CreateIndex
CREATE INDEX "CacheEntry_expiresAt_idx" ON "public"."CacheEntry"("expiresAt");

-- CreateIndex
CREATE INDEX "CacheEntry_tags_idx" ON "public"."CacheEntry"("tags");

-- AddForeignKey
ALTER TABLE "public"."BillingSubscription" ADD CONSTRAINT "BillingSubscription_shop_fkey" FOREIGN KEY ("shop") REFERENCES "public"."Session"("shop") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."BillingPurchase" ADD CONSTRAINT "BillingPurchase_shop_fkey" FOREIGN KEY ("shop") REFERENCES "public"."Session"("shop") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."BillingUsage" ADD CONSTRAINT "BillingUsage_shop_fkey" FOREIGN KEY ("shop") REFERENCES "public"."Session"("shop") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."BillingEvent" ADD CONSTRAINT "BillingEvent_shop_fkey" FOREIGN KEY ("shop") REFERENCES "public"."Session"("shop") ON DELETE CASCADE ON UPDATE CASCADE;
