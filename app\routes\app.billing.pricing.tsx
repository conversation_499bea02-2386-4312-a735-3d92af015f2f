import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { withBulletproofAuth, withBulletproofAction } from "../utils/bulletproof-auth.server";
import { BillingService } from "../services/billing.server";
import { PricingSelection } from "../components/billing/PricingSelection";
import { PageContent } from "@/components/ui/page-header";
import { addCSRFToken } from "../utils/csrf.server";

export const loader = withBulletproofAuth(async ({ auth }) => {
  const { admin, session } = auth;

  // Check if subscription status has been updated recently and force cache refresh if needed
  const { getSubscriptionUpdateFlag, clearSubscriptionUpdateFlag, invalidateBillingCache } = await import("../utils/cache.server");
  const subscriptionUpdateFlag = getSubscriptionUpdateFlag(session.shop);

  if (subscriptionUpdateFlag) {
    console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh on pricing page`);
    // Clear billing cache to ensure we get fresh data
    invalidateBillingCache(session.shop);
    // Clear the flag so we don't keep refreshing unnecessarily
    clearSubscriptionUpdateFlag(session.shop);
  }

  const billingService = new BillingService(admin, session.shop);

  // Get current billing status
  const billingStatus = await billingService.hasActiveBilling();
  const plans = billingService.getAllBillingPlans();
  
  const data = {
    plans,
    currentPlan: billingStatus.plan,
    hasAccess: billingStatus.hasAccess,
    shop: session.shop
  };

  return json(addCSRFToken(session.shop, data));
});

export const action = withBulletproofAction(async (args) => {
  const { handleBillingAction } = await import("../utils/billing-actions.server");
  return handleBillingAction(args);
});

export default function BillingPricingPage() {
  const { plans, currentPlan, csrfToken } = useLoaderData<typeof loader>() as any;

  return (
    <>
      <TitleBar title="Choose Your Plan" />
      <PageContent>
        <PricingSelection
          plans={plans}
          selectedPlan={currentPlan?.id}
          showPayPerUse={true}
          csrfToken={csrfToken}
        />
      </PageContent>
    </>
  );
}
