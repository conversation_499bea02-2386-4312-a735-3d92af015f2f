/**
 * Enhanced Rate Limiting Utility
 * Provides comprehensive rate limiting for API endpoints and user actions
 */

import db from "../db.server";

// Helper functions for type conversion
function convertToNumber(value: any): number {
  if (value === null || value === undefined) return 0;
  if (typeof value === 'bigint') return Number(value);
  if (typeof value === 'number') return value;
  if (typeof value === 'string') return parseFloat(value) || 0;
  return 0;
}

function convertToDate(value: any): Date {
  if (value === null || value === undefined) return new Date();
  if (value instanceof Date) return value;
  if (typeof value === 'string') return new Date(value);
  return new Date();
}
import { createError, type ErrorContext } from "./error-handling.server";

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  identifier: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalHits: number;
}

/**
 * Enhanced rate limiter with database persistence
 */
export class EnhancedRateLimiter {
  private inMemoryStore = new Map<string, { count: number; resetTime: number }>();
  
  /**
   * Check if request is within rate limit
   */
  async checkLimit(config: RateLimitConfig): Promise<RateLimitResult> {
    const now = Date.now();
    const resetTime = now + config.windowMs;
    
    try {
      // Try database first for production persistence
      if (process.env.NODE_ENV === 'production') {
        return await this.checkDatabaseLimit(config, now, resetTime);
      } else {
        // Use in-memory for development
        return this.checkInMemoryLimit(config, now, resetTime);
      }
    } catch (error) {
      console.error('❌ Rate limiting error, falling back to in-memory:', error);
      return this.checkInMemoryLimit(config, now, resetTime);
    }
  }
  
  /**
   * Database-based rate limiting for production
   */
  private async checkDatabaseLimit(
    config: RateLimitConfig, 
    now: number, 
    resetTime: number
  ): Promise<RateLimitResult> {
    const existing = await db.rateLimitEntry.findUnique({
      where: { identifier: config.identifier }
    });
    
    if (existing) {
      const resetTime = convertToDate(existing.resetTime);
      const existingResetTime = resetTime.getTime();
      const count = convertToNumber(existing.count);

      if (now < existingResetTime) {
        // Within the current window
        if (count >= config.maxRequests) {
          return {
            allowed: false,
            remaining: 0,
            resetTime: resetTime,
            totalHits: count
          };
        }

        // Increment counter
        const updated = await db.rateLimitEntry.update({
          where: { identifier: config.identifier },
          data: { count: count + 1 }
        });
        
        return {
          allowed: true,
          remaining: config.maxRequests - convertToNumber(updated.count),
          resetTime: convertToDate(updated.resetTime),
          totalHits: convertToNumber(updated.count)
        };
      } else {
        // Reset the window
        const updated = await db.rateLimitEntry.update({
          where: { identifier: config.identifier },
          data: { 
            count: 1, 
            resetTime: new Date(resetTime) 
          }
        });
        
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: convertToDate(updated.resetTime),
          totalHits: 1
        };
      }
    } else {
      // Create new entry
      const created = await db.rateLimitEntry.create({
        data: {
          identifier: config.identifier,
          count: 1,
          resetTime: new Date(resetTime)
        }
      });
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: created.resetTime,
        totalHits: 1
      };
    }
  }
  
  /**
   * In-memory rate limiting for development
   */
  private checkInMemoryLimit(
    config: RateLimitConfig, 
    now: number, 
    resetTime: number
  ): RateLimitResult {
    const entry = this.inMemoryStore.get(config.identifier);
    
    if (entry) {
      if (now < entry.resetTime) {
        // Within the current window
        if (entry.count >= config.maxRequests) {
          return {
            allowed: false,
            remaining: 0,
            resetTime: new Date(entry.resetTime),
            totalHits: entry.count
          };
        }
        
        // Increment counter
        entry.count++;
        return {
          allowed: true,
          remaining: config.maxRequests - entry.count,
          resetTime: new Date(entry.resetTime),
          totalHits: entry.count
        };
      } else {
        // Reset the window
        this.inMemoryStore.set(config.identifier, { count: 1, resetTime });
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: new Date(resetTime),
          totalHits: 1
        };
      }
    } else {
      // Create new entry
      this.inMemoryStore.set(config.identifier, { count: 1, resetTime });
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: new Date(resetTime),
        totalHits: 1
      };
    }
  }
  
  /**
   * Clean up expired entries
   */
  async cleanup(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'production') {
        // Clean up database
        const deleted = await db.rateLimitEntry.deleteMany({
          where: {
            resetTime: {
              lt: new Date()
            }
          }
        });
        
        if (deleted.count > 0) {
          console.log(`🧹 Cleaned up ${deleted.count} expired rate limit entries`);
        }
      } else {
        // Clean up in-memory storage
        const now = Date.now();
        let cleaned = 0;
        for (const [key, data] of this.inMemoryStore.entries()) {
          if (now > data.resetTime) {
            this.inMemoryStore.delete(key);
            cleaned++;
          }
        }

        if (cleaned > 0) {
          console.log(`🧹 Cleaned up ${cleaned} expired in-memory rate limit entries`);
        }
      }
    } catch (error) {
      console.error('❌ Error cleaning up expired rate limits:', error);
    }
  }
}

// Global rate limiter instance
const globalRateLimiter = new EnhancedRateLimiter();

/**
 * Apply rate limiting with error handling
 */
export async function applyRateLimit(config: RateLimitConfig, context?: ErrorContext): Promise<void> {
  const result = await globalRateLimiter.checkLimit(config);

  if (!result.allowed) {
    const retryAfter = Math.ceil((result.resetTime.getTime() - Date.now()) / 1000);
    throw createError('RATE_LIMIT_EXCEEDED', {
      ...context,
      metadata: {
        identifier: config.identifier,
        retryAfter
      }
    });
  }
}

/**
 * Predefined rate limiters for common use cases
 */
export const RATE_LIMITERS = {
  // Authentication attempts
  AUTH_LOGIN: (ip: string) => ({
    identifier: `auth:login:${ip}`,
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5
  }),

  // CSRF token generation
  CSRF_GENERATION: (shop: string) => ({
    identifier: `csrf:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20
  }),

  // Billing operations
  BILLING_OPERATIONS: (shop: string) => ({
    identifier: `billing:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10
  }),

  // SEO dashboard loading (more permissive)
  SEO_DASHBOARD: (shop: string) => ({
    identifier: `seo_dashboard:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20 // Allow more dashboard loads
  }),

  // SEO optimization requests (stricter)
  SEO_OPTIMIZATION: (shop: string) => ({
    identifier: `seo_opt:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10 // Allow more optimizations
  }),

  // SEO progress polling (very permissive)
  SEO_PROGRESS: (shop: string) => ({
    identifier: `seo_progress:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50 // Allow frequent polling
  }),

  // General API requests
  API_GENERAL: (shop: string) => ({
    identifier: `api:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100
  }),

  // API Billing requests
  API_BILLING: (shop: string) => ({
    identifier: `api_billing:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10
  }),

  // Webhook processing
  WEBHOOK_PROCESSING: (shop: string) => ({
    identifier: `webhook:${shop}`,
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50
  }),

  // Additional rate limiters for services
  AUTH_ATTEMPTS: (ip: string) => ({
    identifier: `auth_attempts:${ip}`,
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5
  })
};

// Schedule cleanup every 5 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    globalRateLimiter.cleanup();
  }, 5 * 60 * 1000);
}


