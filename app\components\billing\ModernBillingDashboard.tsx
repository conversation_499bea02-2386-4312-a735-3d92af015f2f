import * as React from "react";
import { useFetcher } from "@remix-run/react";
import { Button as UIButton } from "@/components/ui/button";
import { BillingPlan, SubscriptionData } from "../../services/billing.server";
import { PricingSelection } from "./PricingSelection";
import { CreditsDashboard } from "./CreditsDashboard";

interface BillingDashboardProps {
  subscription?: SubscriptionData;
  plan?: BillingPlan;
  monthlyUsage?: {
    productsOptimized: number;
    totalSpent: number;
    lastOptimization?: string;
  };
  recentPurchases?: Array<{
    id: string;
    amount: number;
    productCount: number;
    date: string;
    status: string;
  }>;
  plans?: BillingPlan[];
  csrfToken?: string;
  creditBalance?: {
    totalCredits: number;
    usedCredits: number;
    remainingCredits: number;
    lastUpdated: Date;
  };
  creditHistory?: Array<{
    type: string;
    amount: number;
    description: string;
    date: Date;
    referenceId?: string;
  }>;
}

export function ModernBillingDashboard({
  subscription,
  plan,
  monthlyUsage,
  recentPurchases = [],
  plans = [],
  csrfToken,
  creditBalance,
  creditHistory = []
}: BillingDashboardProps) {
  const fetcher = useFetcher();
  const [showPricingSelection, setShowPricingSelection] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [userInteracted, setUserInteracted] = React.useState(false);

  // Track user interaction to prevent automatic cancellation
  React.useEffect(() => {
    const handleUserInteraction = () => {
      console.log('👆 User interaction detected');
      setUserInteracted(true);
    };

    // Add event listeners for user interaction
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);
    document.addEventListener('touchstart', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
    };
  }, []);

  // Debug logging to understand what data we're receiving
  React.useEffect(() => {
    console.log('🔍 ModernBillingDashboard Debug:', {
      subscription,
      plan: {
        id: plan?.id,
        name: plan?.name,
        price: plan?.price,
        type: plan?.type
      },
      hasActiveSubscription: hasActiveSubscription(),
      statusDisplay: getStatusDisplay()
    });
  }, [subscription, plan]);

  // Auto-refresh mechanism: DISABLED
  // The server-side auto-sync in the billing loader is working perfectly
  // Client-side auto-refresh was causing authentication issues
  // Users can manually refresh using the "Refresh Subscription" button if needed

  React.useEffect(() => {
    if (fetcher.data && typeof fetcher.data === 'object') {
      const data = fetcher.data as any;
      if (data.error) {
        alert(`Error: ${data.error}`);
      } else if (data.success) {
        if (data.redirectTo) {
          window.location.href = data.redirectTo;
        } else {
          window.location.reload();
        }
      }
    }
    setIsRefreshing(fetcher.state === 'loading' || fetcher.state === 'submitting');
  }, [fetcher.data, fetcher.state]);

  const handleCancelSubscription = () => {
    console.log('🚨 handleCancelSubscription called - this should only happen when user clicks Cancel button');
    console.log('🔍 Call stack:', new Error().stack);
    console.log('🔍 User interacted:', userInteracted);

    // Safety check: Only allow cancellation if user has interacted with the page
    if (!userInteracted) {
      console.log('🛡️ BLOCKING automatic cancellation - user has not interacted with page yet');
      return;
    }

    if (!subscription || fetcher.state === 'submitting') {
      console.log('❌ Cannot cancel - no subscription or already submitting');
      return;
    }

    const confirmMessage = `Are you sure you want to cancel your ${plan?.name || 'subscription'}?`;
    console.log('🤔 Showing confirmation dialog:', confirmMessage);

    if (confirm(confirmMessage)) {
      console.log('✅ User confirmed cancellation');
      const formData = new FormData();
      formData.append('action', 'cancel_subscription');
      formData.append('subscriptionId', subscription.id);
      if (csrfToken) formData.append('csrfToken', csrfToken);
      fetcher.submit(formData, { method: 'POST' });
    } else {
      console.log('❌ User cancelled the cancellation');
    }
  };

  const getStatusDisplay = () => {
    if (!subscription) return { text: 'No Active Plan', color: 'text-gray-400' };

    switch (subscription.status) {
      case 'ACTIVE':
        // Show more specific status for active subscriptions
        if (hasActiveSubscription()) {
          return { text: 'Active Subscription', color: 'text-green-400' };
        } else {
          return { text: 'Active', color: 'text-green-400' };
        }
      case 'PENDING': return { text: 'Pending', color: 'text-yellow-400' };
      case 'CANCELLED': return { text: 'Cancelled', color: 'text-red-400' };
      case 'EXPIRED': return { text: 'Expired', color: 'text-red-400' };
      default: return { text: subscription.status, color: 'text-gray-400' };
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  const getPlanDisplayName = () => {
    // If we have an active subscription but no plan data, infer from subscription
    if (subscription && subscription.status === 'ACTIVE') {
      if (plan?.name) {
        return plan.name;
      }
      // Try to infer plan from subscription data
      const lineItem = subscription.lineItems?.[0];
      if (lineItem?.plan?.pricingDetails) {
        const interval = lineItem.plan.pricingDetails.interval;
        if (interval === 'ANNUAL') return 'Annual Plan';
        if (interval === 'EVERY_30_DAYS') return 'Monthly Plan';
      }
      return 'Active Subscription';
    }

    return plan?.name || 'No Plan';
  };

  const getPlanDisplayPrice = () => {
    // If we have an active subscription but no plan data, infer from subscription
    if (subscription && subscription.status === 'ACTIVE') {
      if (plan?.price) {
        return formatCurrency(plan.price);
      }
      // Try to infer price from subscription data
      const lineItem = subscription.lineItems?.[0];
      if (lineItem?.plan?.pricingDetails?.price?.amount) {
        const amount = parseFloat(lineItem.plan.pricingDetails.price.amount);
        return formatCurrency(amount);
      }
      return 'Active';
    }

    return plan?.price ? formatCurrency(plan.price) : '$0';
  };

  const getPlanDisplayInterval = () => {
    // If we have an active subscription but no plan data, infer from subscription
    if (subscription && subscription.status === 'ACTIVE') {
      if (plan?.type) {
        return plan.type === 'pay_per_use' ? 'Per Use' :
               plan.type === 'annual' ? 'Annual' : 'Monthly';
      }
      // Try to infer interval from subscription data
      const lineItem = subscription.lineItems?.[0];
      if (lineItem?.plan?.pricingDetails) {
        const interval = lineItem.plan.pricingDetails.interval;
        if (interval === 'ANNUAL') return 'Annual';
        if (interval === 'EVERY_30_DAYS') return 'Monthly';
      }
      return 'Subscription';
    }

    return plan?.type === 'pay_per_use' ? 'Per Use' : 'Monthly';
  };

  const hasActiveSubscription = () => {
    // Only show unlimited access if we have a clear active subscription
    // AND we can determine it's a subscription plan (not pay-per-use)
    if (!subscription || subscription.status !== 'ACTIVE') {
      return false;
    }

    // If we have plan data, check if it's a subscription plan
    if (plan) {
      return plan.type !== 'pay_per_use';
    }

    // If no plan data but we have subscription data, infer from subscription
    const lineItem = subscription.lineItems?.[0];
    if (lineItem?.plan?.pricingDetails) {
      const interval = lineItem.plan.pricingDetails.interval;
      // Only show unlimited for recurring subscriptions, not one-time purchases
      return interval === 'ANNUAL' || interval === 'EVERY_30_DAYS';
    }

    // If we can't determine the plan type, be conservative
    return false;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'short', day: 'numeric'
    });
  };

  // Auto-sync loading state removed since we're using server-side detection

  if (showPricingSelection) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <UIButton
            onClick={() => setShowPricingSelection(false)}
            className="mb-4 bg-white/10 border border-white/20 text-white hover:bg-white/20"
          >
            ← Back to Billing Dashboard
          </UIButton>
        </div>
        <PricingSelection
          plans={plans}
          selectedPlan={plan?.id}
          csrfToken={csrfToken}
          hasActiveSubscription={hasActiveSubscription()}
        />
      </div>
    );
  }

  // Auto-sync loading state removed - using server-side detection instead

  const statusDisplay = getStatusDisplay();

  // Debug logging (remove in production)
  console.log('🔍 ModernBillingDashboard Debug:', {
    subscription: subscription ? {
      id: subscription.id,
      status: subscription.status,
      lineItems: subscription.lineItems
    } : null,
    plan: plan ? {
      id: plan.id,
      name: plan.name,
      price: plan.price,
      type: plan.type
    } : null,
    hasActiveSubscription: hasActiveSubscription(),
    statusDisplay
  });

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Test Mode Banner */}
      <div className="bg-yellow-500/20 border border-yellow-500/40 rounded-2xl p-4 text-center">
        <div className="text-yellow-300 font-bold text-lg">🧪 TEST MODE ENABLED</div>
        <div className="text-yellow-200 text-sm mt-1">All payments are in test mode - no real charges will be made</div>
      </div>

      {/* Current Plan Status */}
      <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold mb-2">Current Plan</h2>
            <p className="text-white/70">Manage your subscription and billing preferences</p>
          </div>
          <div className={`text-lg font-semibold ${statusDisplay.color}`}>
            {statusDisplay.text}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {getPlanDisplayName()}
            </div>
            <div className="text-white/70">Current Plan</div>
          </div>

          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {getPlanDisplayPrice()}
            </div>
            <div className="text-white/70">
              {getPlanDisplayInterval()}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {monthlyUsage?.productsOptimized || 0}
            </div>
            <div className="text-white/70">Products Optimized</div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <UIButton
            onClick={() => setShowPricingSelection(true)}
            className="bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl"
            disabled={isRefreshing}
          >
            {plan ? 'Change Plan' : 'Choose Plan'}
          </UIButton>

          {/* Manual refresh button for pay-per-use plans */}
          {plan?.id === 'pay_per_use' && (
            <UIButton
              onClick={() => {
                setIsRefreshing(true);
                if (typeof window !== 'undefined') {
                  window.location.href = window.location.pathname + '?refresh=true';
                }
              }}
              className="bg-transparent border-2 border-blue-400/60 text-blue-400 hover:bg-blue-400 hover:text-black font-bold py-3 px-8 rounded-2xl"
              disabled={isRefreshing}
            >
              {isRefreshing ? 'Refreshing...' : 'Refresh Subscription'}
            </UIButton>
          )}

          {subscription && subscription.status === 'ACTIVE' && (
            <UIButton
              onClick={handleCancelSubscription}
              className="bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl"
              disabled={isRefreshing}
            >
              Cancel Subscription
            </UIButton>
          )}
        </div>
      </div>

      {/* Usage Statistics */}
      {monthlyUsage && (
        <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
          <h2 className="text-2xl font-bold mb-6">Usage This Month</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <div className="text-3xl font-bold mb-2">
                {formatCurrency(monthlyUsage.totalSpent)}
              </div>
              <div className="text-white/70">Total Spent</div>
            </div>
            
            <div>
              <div className="text-3xl font-bold mb-2">
                {monthlyUsage.lastOptimization ? formatDate(monthlyUsage.lastOptimization) : 'Never'}
              </div>
              <div className="text-white/70">Last Optimization</div>
            </div>
          </div>
        </div>
      )}

      {/* Credits Dashboard */}
      {creditBalance && (
        <CreditsDashboard
          balance={creditBalance}
          history={creditHistory}
          hasSubscription={hasActiveSubscription()}
        />
      )}

      {/* Recent Purchases */}
      {recentPurchases.length > 0 && (
        <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
          <h2 className="text-2xl font-bold mb-6">Recent Purchases</h2>

          <div className="space-y-4">
            {recentPurchases.slice(0, 5).map((purchase) => (
              <div key={purchase.id} className="flex justify-between items-center py-4 border-b border-white/10 last:border-b-0">
                <div>
                  <div className="font-semibold">{purchase.productCount} Products Optimized</div>
                  <div className="text-white/70 text-sm">{formatDate(purchase.date)}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold">{formatCurrency(purchase.amount)}</div>
                  <div className={`text-sm ${purchase.status === 'ACTIVE' ? 'text-green-400' : 'text-gray-400'}`}>
                    {purchase.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
