import type { HeadersFunction, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { Link, Outlet, useLoaderData, useRouteError } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import { NavMenu } from "@shopify/app-bridge-react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { InstantNavigationProvider } from "@/components/navigation/InstantNavigation";

import { generateCSRFToken } from "../utils/csrf.server";
import { withBulletproofAuth } from "../utils/bulletproof-auth.server";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = withBulletproofAuth(async ({ auth }) => {
  const { admin, session } = auth;

  console.log(`🔐 [APP-LAYOUT] Loading app layout for shop: ${session.shop}`);

  // Generate CSRF token for the session
  const csrfToken = generateCSRFToken(session.shop);

  console.log(`✅ [APP-LAYOUT] Session validated for shop: ${session.shop}`);

  return {
    apiKey: process.env.SHOPIFY_API_KEY || "",
    shop: session.shop,
    csrfToken,
    sessionValid: true
  };
});

export default function App() {
  const { apiKey } = useLoaderData<typeof loader>();

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <NavMenu>
        <Link to="/app" rel="home">
          AI BULK SEO Dashboard
        </Link>
        <Link to="/app/seo-dashboard">SEO Optimizer</Link>
        <Link to="/app/billing">Billing</Link>
        <Link to="/app/settings">Settings</Link>
      </NavMenu>
      <InstantNavigationProvider>
        <Outlet />
      </InstantNavigationProvider>
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  const error = useRouteError();
  console.error('App route error:', error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-xl font-bold text-red-600 mb-4">Something went wrong</h1>
        <p className="text-gray-600 mb-4">
          We encountered an error while loading the application. Please try refreshing the page.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
