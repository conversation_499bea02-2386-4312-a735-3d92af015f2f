/**
 * Security Service
 * Handles authentication security, input validation, and security monitoring
 */

import { createHash, randomBytes, timingSafeEqual } from "crypto";
import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";

export interface SecurityConfig {
  maxLoginAttempts: number;
  lockoutDurationMs: number;
  sessionTimeoutMs: number;
  csrfTokenTTL: number;
  passwordMinLength: number;
  requireStrongPasswords: boolean;
}

export interface SecurityEvent {
  type: 'login_attempt' | 'login_success' | 'login_failure' | 'session_expired' | 'csrf_violation' | 'suspicious_activity';
  shop: string;
  userId?: string;
  ip: string;
  userAgent: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface LoginAttempt {
  shop: string;
  ip: string;
  attempts: number;
  lastAttempt: Date;
  lockedUntil?: Date;
}

/**
 * Enhanced security service
 */
export class SecurityService {
  private static instance: SecurityService;
  private config: SecurityConfig = {
    maxLoginAttempts: 5,
    lockoutDurationMs: 15 * 60 * 1000, // 15 minutes
    sessionTimeoutMs: 24 * 60 * 60 * 1000, // 24 hours
    csrfTokenTTL: 60 * 60 * 1000, // 1 hour
    passwordMinLength: 12,
    requireStrongPasswords: true
  };

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  /**
   * Validate authentication request with security checks
   */
  async validateAuthRequest(
    shop: string,
    ip: string,
    userAgent: string,
    context?: ErrorContext
  ): Promise<void> {
    try {
      // Apply rate limiting
      await applyRateLimit(RATE_LIMITERS.AUTH_ATTEMPTS(ip), context);

      // Check for account lockout
      const lockoutStatus = await this.checkAccountLockout(shop, ip);
      if (lockoutStatus.isLocked) {
        await this.logSecurityEvent({
          type: 'login_attempt',
          shop,
          ip,
          userAgent,
          metadata: { 
            reason: 'account_locked',
            lockedUntil: lockoutStatus.lockedUntil 
          },
          timestamp: new Date()
        });

        throw createError('AUTH_ACCOUNT_LOCKED', {
          ...context,
          shop,
          metadata: { 
            lockedUntil: lockoutStatus.lockedUntil,
            remainingTime: lockoutStatus.remainingTime 
          }
        });
      }

      // Check for suspicious patterns
      await this.detectSuspiciousActivity(shop, ip, userAgent, context);

    } catch (error) {
      console.error('❌ Auth validation failed:', error);
      throw error;
    }
  }

  /**
   * Record login attempt
   */
  async recordLoginAttempt(
    shop: string,
    ip: string,
    userAgent: string,
    success: boolean,
    context?: ErrorContext
  ): Promise<void> {
    try {
      const eventType = success ? 'login_success' : 'login_failure';
      
      await this.logSecurityEvent({
        type: eventType,
        shop,
        ip,
        userAgent,
        timestamp: new Date()
      });

      if (!success) {
        await this.incrementFailedAttempts(shop, ip);
      } else {
        await this.clearFailedAttempts(shop, ip);
      }

    } catch (error) {
      console.error('❌ Failed to record login attempt:', error);
      await logError(
        error instanceof Error ? error : new Error('Login attempt recording failed'),
        { ...context, shop, action: 'record_login_attempt' }
      );
    }
  }

  /**
   * Generate secure CSRF token
   */
  async generateCSRFToken(shop: string, context?: ErrorContext): Promise<string> {
    try {
      const token = randomBytes(32).toString('hex');
      const hashedToken = this.hashToken(token);
      const expires = new Date(Date.now() + this.config.csrfTokenTTL);

      await db.cSRFToken.create({
        data: {
          id: hashedToken,
          token: hashedToken,
          shop,
          expires
        }
      });

      console.log(`🔐 CSRF token generated for shop: ${shop}`);
      return token;

    } catch (error) {
      console.error('❌ CSRF token generation failed:', error);
      await logError(
        error instanceof Error ? error : new Error('CSRF token generation failed'),
        { ...context, shop, action: 'generate_csrf_token' }
      );
      throw error;
    }
  }

  /**
   * Validate CSRF token
   */
  async validateCSRFToken(
    token: string,
    shop: string,
    context?: ErrorContext
  ): Promise<boolean> {
    try {
      const hashedToken = this.hashToken(token);

      const storedToken = await db.cSRFToken.findUnique({
        where: { id: hashedToken }
      });

      if (!storedToken) {
        await this.logSecurityEvent({
          type: 'csrf_violation',
          shop,
          ip: 'unknown',
          userAgent: 'unknown',
          metadata: { reason: 'token_not_found' },
          timestamp: new Date()
        });
        return false;
      }

      // Type guard to check if storedToken has shop property
      if ('shop' in storedToken && storedToken.shop !== shop) {
        await this.logSecurityEvent({
          type: 'csrf_violation',
          shop,
          ip: 'unknown',
          userAgent: 'unknown',
          metadata: { reason: 'shop_mismatch', expectedShop: storedToken.shop },
          timestamp: new Date()
        });
        return false;
      }

      const expiresDate = storedToken.expires ? new Date(storedToken.expires) : new Date();
      if (new Date() > expiresDate) {
        await this.logSecurityEvent({
          type: 'csrf_violation',
          shop,
          ip: 'unknown',
          userAgent: 'unknown',
          metadata: { reason: 'token_expired' },
          timestamp: new Date()
        });

        // Clean up expired token
        await db.cSRFToken.delete({ where: { id: hashedToken } });
        return false;
      }

      // Token is valid - optionally rotate it
      console.log(`✅ CSRF token validated for shop: ${shop}`);
      return true;

    } catch (error) {
      console.error('❌ CSRF token validation failed:', error);
      await logError(
        error instanceof Error ? error : new Error('CSRF token validation failed'),
        { ...context, shop, action: 'validate_csrf_token' }
      );
      return false;
    }
  }

  /**
   * Sanitize user input to prevent XSS and injection attacks
   */
  sanitizeInput(input: string, options?: { allowHtml?: boolean; maxLength?: number }): string {
    if (typeof input !== 'string') {
      return '';
    }

    let sanitized = input.trim();

    // Limit length
    if (options?.maxLength) {
      sanitized = sanitized.substring(0, options.maxLength);
    }

    // Remove or escape HTML if not allowed
    if (!options?.allowHtml) {
      sanitized = sanitized
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
    }

    // Remove null bytes and control characters
    sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '');

    return sanitized;
  }

  /**
   * Validate session security
   */
  async validateSession(
    sessionId: string,
    shop: string,
    ip: string,
    userAgent: string,
    context?: ErrorContext
  ): Promise<boolean> {
    try {
      const session = await db.session.findUnique({
        where: { id: sessionId }
      });

      if (!session) {
        await this.logSecurityEvent({
          type: 'session_expired',
          shop,
          ip,
          userAgent,
          metadata: { reason: 'session_not_found' },
          timestamp: new Date()
        });
        return false;
      }

      if (session.shop !== shop) {
        await this.logSecurityEvent({
          type: 'suspicious_activity',
          shop,
          ip,
          userAgent,
          metadata: { 
            reason: 'shop_mismatch',
            sessionShop: session.shop,
            requestShop: shop
          },
          timestamp: new Date()
        });
        return false;
      }

      if (session.expires && new Date() > session.expires) {
        await this.logSecurityEvent({
          type: 'session_expired',
          shop,
          ip,
          userAgent,
          metadata: { reason: 'session_expired' },
          timestamp: new Date()
        });
        return false;
      }

      return true;

    } catch (error) {
      console.error('❌ Session validation failed:', error);
      await logError(
        error instanceof Error ? error : new Error('Session validation failed'),
        { ...context, shop, action: 'validate_session' }
      );
      return false;
    }
  }

  /**
   * Private helper methods
   */
  private async checkAccountLockout(shop: string, ip: string): Promise<{
    isLocked: boolean;
    lockedUntil?: Date;
    remainingTime?: number;
  }> {
    // Implementation would check database for failed attempts
    // For now, return not locked
    return { isLocked: false };
  }

  private async incrementFailedAttempts(shop: string, ip: string): Promise<void> {
    // Implementation would increment failed attempts counter
    console.log(`⚠️ Failed login attempt recorded for ${shop} from ${ip}`);
  }

  private async clearFailedAttempts(shop: string, ip: string): Promise<void> {
    // Implementation would clear failed attempts counter
    console.log(`✅ Failed attempts cleared for ${shop} from ${ip}`);
  }

  private async detectSuspiciousActivity(
    shop: string,
    ip: string,
    userAgent: string,
    context?: ErrorContext
  ): Promise<void> {
    // Implementation would detect patterns like:
    // - Multiple shops from same IP
    // - Unusual user agents
    // - Geographic anomalies
    // - Time-based patterns
    console.log(`🔍 Checking for suspicious activity: ${shop} from ${ip}`);
  }

  private async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Log to database or external security service
      console.log(`🔒 Security event: ${event.type} for ${event.shop} from ${event.ip}`);
      
      // In a real implementation, you might want to:
      // - Store in a dedicated security events table
      // - Send to external SIEM system
      // - Trigger alerts for critical events
      
    } catch (error) {
      console.error('❌ Failed to log security event:', error);
    }
  }

  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }
}

// Export singleton instance
export const securityService = SecurityService.getInstance();
