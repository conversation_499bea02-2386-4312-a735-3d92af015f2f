import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { Form, useLoaderData } from "@remix-run/react";
import { login } from "../../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }

  return { showForm: Boolean(login) };
};

export default function App() {
  const { showForm } = useLoaderData<typeof loader>();

  return (
    <div
      style={{
        backgroundColor: "#000000", // Pure black background
        color: "#ffffff", // Pure white text
        fontFamily:
          'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "2rem",
      }}
    >
      <div
        style={{
          maxWidth: "640px",
          width: "100%",
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          gap: "1rem",
        }}
      >
        <h1
          style={{
            fontSize: "3rem",
            fontWeight: "800",
            color: "#ffffff", // Pure white for heading
            letterSpacing: "-0.025em",
            lineHeight: "1.1",
            margin: "0",
            display: "flex", // Enable flexbox for centering children
            flexDirection: "column", // Stack children vertically
            alignItems: "center", // Center horizontally
            justifyContent: "center", // Center vertically (if needed for the h1 itself)
          }}
        >
          <img
            src="/logo.png"
            alt="AI BULK SEO Logo"
            className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
            style={{ filter: 'brightness(1.1) contrast(1.1)' }} // Pure black and white logo
          />
          <div className="text-sm font-semibold tracking-widest uppercase mb-4"
            style={{ color: "#ffffff" }} // Pure white text for AI BULK SEO
          >
            AI BULK SEO
          </div>
          Automate Your Shopify SEO with AI BULK SEO 🚀
        </h1>
        <p
          style={{
            fontSize: "1.125rem",
            color: "#e0e0e0", // Slightly off-white for body text for readability
            lineHeight: "1.75",
            maxWidth: "560px",
            margin: "0 auto 1.5rem auto",
          }}
        >
          Effortlessly optimize your entire product catalog for search engines
          in minutes, not months. Let our AI handle the heavy lifting.
        </p>
        {showForm && (
          <Form
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "1rem",
              width: "100%",
              maxWidth: "384px",
              margin: "0 auto",
            }}
            method="post"
            action="/auth/login"
          >
            <label
              style={{
                display: "flex",
                flexDirection: "column",
                textAlign: "left",
                gap: "0.5rem",
              }}
            >
              <span
                style={{
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "#ffffff", // Pure white for label
                }}
              >
                Shop Domain
              </span>
              <input
                style={{
                  backgroundColor: "#000000", // Pure black input background
                  border: "1px solid #404040", // Dark grey border for subtle definition
                  color: "#ffffff", // Pure white input text
                  borderRadius: "0.5rem",
                  padding: "0.75rem 1rem",
                  fontSize: "1rem",
                  outline: "none",
                }}
                type="text"
                name="shop"
                placeholder="your-store-name.myshopify.com"
              />
            </label>
            <button
              style={{
                backgroundColor: "#ffffff", // Pure white button background
                color: "#000000", // Pure black button text
                fontWeight: "600",
                padding: "0.75rem 1rem",
                borderRadius: "0.5rem",
                border: "none",
                cursor: "pointer",
                fontSize: "1rem",
              }}
              type="submit"
            >
              Install & Log In
            </button>
          </Form>
        )}
        <ul
          style={{
            listStyle: "none",
            padding: "0",
            marginTop: "3rem",
            display: "grid",
            gap: "1rem",
            textAlign: "left",
            width: "100%",
          }}
        >
          <li
            style={{
              backgroundColor: "#1a1a1a", // Dark grey for card background, close to black but with some differentiation
              padding: "1.5rem",
              borderRadius: "0.75rem",
              border: "1px solid #404040", // Dark grey border for definition
            }}
          >
            <strong
              style={{
                color: "#ffffff", // Pure white for strong text
                display: "block",
                marginBottom: "0.25rem",
                fontWeight: "600",
                fontSize: "1rem",
              }}
            >
              🤖 AI-Powered Bulk Optimization
            </strong>
            <span style={{ color: "#e0e0e0", fontSize: "0.875rem" }}>
              Revamp thousands of product titles, descriptions, and meta tags in
              a single click. Our AI analyzes your products and generates
              SEO-friendly content that converts.
            </span>
          </li>
          <li
            style={{
              backgroundColor: "#1a1a1a", // Dark grey for card background
              padding: "1.5rem",
              borderRadius: "0.75rem",
              border: "1px solid #404040", // Dark grey border for definition
            }}
          >
            <strong
              style={{
                color: "#ffffff", // Pure white for strong text
                display: "block",
                marginBottom: "0.25rem",
                fontWeight: "600",
                fontSize: "1rem",
              }}
            >
              🔑 Smart Keyword Suggestions
            </strong>
            <span style={{ color: "#e0e0e0", fontSize: "0.875rem" }}>
              Discover high-intent, low-competition keywords tailored to each
              product. Stop guessing and start ranking for terms your customers
              are actually searching for.
            </span>
          </li>
          <li
            style={{
              backgroundColor: "#1a1a1a", // Dark grey for card background
              padding: "1.5rem",
              borderRadius: "0.75rem",
              border: "1px solid #404040", // Dark grey border for definition
            }}
          >
            <strong
              style={{
                color: "#ffffff", // Pure white for strong text
                display: "block",
                marginBottom: "0.25rem",
                fontWeight: "600",
                fontSize: "1rem",
              }}
            >
              📈 Real-time Performance Tracking
            </strong>
            <span style={{ color: "#e0e0e0", fontSize: "0.875rem" }}>
              Monitor your SEO progress with a clear, intuitive dashboard. Watch
              your products climb the search rankings and see the direct impact
              of your optimizations.
            </span>
          </li>
        </ul>
      </div>
    </div>
  );
}