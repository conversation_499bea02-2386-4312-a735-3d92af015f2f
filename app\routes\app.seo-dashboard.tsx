import { useState, useCallback, useEffect, useRef } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderD<PERSON>, useFetcher, useNavigate } from "@remix-run/react";
import { motion, AnimatePresence, useAnimation } from "framer-motion";
import {
  LoadingAnimation,
  SuccessAnimation,
  ProcessingAnimation,
  FloatingElementsAnimation,
  MorphingShapeAnimation,
  BulletPointAnimation,
  SeoIconAnimation
} from "../components/LottieAnimations";
import { Button as ShadcnButton } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { TitleBar } from "@shopify/app-bridge-react";
import { withBulletproofAuth, withBulletproofAction } from "../utils/bulletproof-auth.server";
import { GeminiService } from "../services/gemini.server";
import { BillingService, SubscriptionData } from "../services/billing.server";
import { CreditsService } from "../services/credits.server";
import { createGraphQLService } from "../services/graphql.server";
import { createSEOOptimizationService } from "../services/seo-optimization.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";
import { SeoOptimizationHeader } from "@/components/headers/SeoOptimizationHeader";
import { BillingStatus } from "../components/billing/BillingStatus";
import { PayPerUseConfirmation } from "../components/billing/PayPerUseConfirmation";
import { useBillingAccess, usePayPerUseBilling } from "../hooks/useBillingAccess";
import { generateCSRFToken } from "../utils/csrf.server";

interface SeoOptimizationSettings {
  updateProductTitle: boolean;
  updateProductDescription: boolean;
  updateSeoFields: boolean;
  updateHandle: boolean;
  updateImageAlts: boolean;
  autoApply: boolean;
  batchSize: number;
}

interface ProductSeoData {
  id: string;
  title: string;
  description: string;
  type: string;
  vendor: string;
  seoTitle: string;
  seoDescription: string;
  handle: string;
  seoScore: number;
  status: 'pending' | 'processing' | 'optimized' | 'failed';
  lastOptimized?: string;
  viralKeyword?: string;
  targetKeywords?: string[];
}

export const loader = withBulletproofAuth(async ({ request, auth }) => {
  const { admin, session } = auth;
  const url = new URL(request.url);
  
  // Get pagination and filtering parameters
  const page = parseInt(url.searchParams.get('page') || '1');
  const search = url.searchParams.get('search') || '';

  try {
    // Apply rate limiting for SEO dashboard access
    await applyRateLimit(RATE_LIMITERS.SEO_DASHBOARD(session.shop), {
      action: 'seo_dashboard_load',
      shop: session.shop
    });

    // Create GraphQL service instance
    const graphqlService = createGraphQLService(admin, session.shop);

    // Fetch products using the enhanced GraphQL service
    console.log('📦 Fetching products using enhanced GraphQL service...');
    const searchQuery = search ? `title:*${search}* OR description:*${search}*` : undefined;
    const fetchedProducts = await graphqlService.getAllProducts(searchQuery, 1000, {
      action: 'fetch_products_for_seo',
      shop: session.shop
    });

    // Transform products for the dashboard
    const allProducts = fetchedProducts.map((product: any) => {
      return {
        id: product.id.replace("gid://shopify/Product/", ""),
        title: product.title,
        description: product.description || "",
        type: product.productType || "Uncategorized",
        vendor: product.vendor || "Unknown",
        handle: product.handle,
        seoTitle: product.seo?.title || "",
        seoDescription: product.seo?.description || "",
        seoScore: calculateSeoScore(product),
        status: 'pending',
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      };
    });

    console.log(`✅ Successfully fetched ${allProducts.length} products using GraphQL service`);
    const products = allProducts;

    // Get unique product types for filtering
    const productTypes = [...new Set(products.map((p: any) => p.type))].filter(Boolean) as string[];

    // Check if subscription status has been updated recently and force cache refresh if needed
    const { getSubscriptionUpdateFlag, clearSubscriptionUpdateFlag, invalidateBillingCache } = await import("../utils/cache.server");
    const subscriptionUpdateFlag = getSubscriptionUpdateFlag(session.shop);

    if (subscriptionUpdateFlag) {
      console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh`);
      // Clear billing cache to ensure we get fresh data
      invalidateBillingCache(session.shop);
      // Clear the flag so we don't keep refreshing unnecessarily
      clearSubscriptionUpdateFlag(session.shop);
    }

    // Get billing information
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();

    // Get credit information
    const creditsService = new CreditsService(session.shop);
    const creditBalance = await creditsService.getCreditBalance();

    // Generate CSRF token for pay-per-use operations
    const csrfToken = generateCSRFToken(session.shop);
    console.log('🔐 Generated CSRF token for SEO dashboard:', session.shop);

    return json({
      products,
      productTypes,
      pagination: {
        currentPage: page,
        hasNextPage: false, // We fetched all available products
        hasPreviousPage: false,
      },
      totalProducts: products.length,
      billing: {
        hasAccess: billingStatus.hasAccess,
        plan: billingStatus.plan,
        subscription: billingStatus.subscription
      },
      creditBalance,
      csrfToken
    });
  } catch (error) {
    console.error('Error loading products:', error);
    return json({
      products: [],
      productTypes: [],
      pagination: { currentPage: 1, hasNextPage: false, hasPreviousPage: false },
      totalProducts: 0,
      billing: {
        hasAccess: false,
        plan: undefined,
        subscription: undefined
      },
      error: 'Failed to load products'
    });
  }
});

// Helper function to calculate basic SEO score
function calculateSeoScore(product: any): number {
  let score = 0;
  
  // Title optimization (30 points)
  if (product.title) {
    score += Math.min(30, product.title.length > 10 ? 30 : 15);
  }
  
  // Description optimization (25 points)
  if (product.description && product.description.length > 50) {
    score += 25;
  } else if (product.description) {
    score += 10;
  }
  
  // SEO title (25 points)
  if (product.seo?.title) {
    score += product.seo.title.length <= 70 ? 25 : 15;
  }
  
  // SEO description (20 points)
  if (product.seo?.description) {
    score += product.seo.description.length <= 160 ? 20 : 10;
  }
  
  return Math.min(100, score);
}

// Simple in-memory cache to prevent duplicate requests
const activeRequests = new Map<string, boolean>();

export const action = withBulletproofAction(async ({ request, auth }) => {
  const { admin } = auth;
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "optimize_comprehensive") {
    const productIds = JSON.parse(formData.get("productIds")?.toString() || "[]");
    const settings = JSON.parse(formData.get("settings")?.toString() || "{}");
    const apiKey = process.env.GEMINI_API_KEY;
    const requestId = formData.get("requestId")?.toString() || `${Date.now()}-${Math.random()}`;

    // Prevent duplicate requests
    if (activeRequests.has(requestId)) {
      console.log(`⚠️ Duplicate request detected: ${requestId}`);
      return json({
        error: "Request already in progress. Please wait for the current optimization to complete.",
        isDuplicate: true
      }, { status: 429 });
    }

    activeRequests.set(requestId, true);

    try {
      if (!apiKey) {
        activeRequests.delete(requestId);
        return json({ error: "Gemini API key not configured on server" }, { status: 500 });
      }

      if (productIds.length === 0) {
        activeRequests.delete(requestId);
        return json({ error: "No products selected" }, { status: 400 });
      }

      try {
      console.log(`🚀 Starting comprehensive SEO optimization for ${productIds.length} products`);
      
      // Process products in smaller batches to avoid query cost limits
      const batchSize = 10; // Reduced batch size for GraphQL query limits
      const allProducts = [];

      for (let i = 0; i < productIds.length; i += batchSize) {
        const batchIds = productIds.slice(i, i + batchSize);

        console.log(`📦 Fetching batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(productIds.length / batchSize)} (${batchIds.length} products)`);

        const productQueries = batchIds.map((id: string, index: number) => `
          product${index}: product(id: "gid://shopify/Product/${id}") {
            id
            title
            description
            productType
            vendor
            handle
            seo {
              title
              description
            }
            images(first: 3) {
              edges {
                node {
                  id
                  altText
                }
              }
            }
          }
        `).join('\n');

        const response = await admin.graphql(`
          query getProductsForOptimization {
            ${productQueries}
          }
        `);

        const responseJson = await response.json();
        const batchProducts = Object.values(responseJson.data || {});
        allProducts.push(...batchProducts);

        // Small delay between batches to be respectful to Shopify's API
        if (i + batchSize < productIds.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      const products = allProducts.map((product: any) => ({
        id: product.id.replace("gid://shopify/Product/", ""),
        title: product.title,
        description: product.description || "",
        type: product.productType || "Uncategorized",
        vendor: product.vendor || "Unknown",
        handle: product.handle,
        seoTitle: product.seo?.title || "",
        seoDescription: product.seo?.description || "",
        images: product.images?.edges?.map((img: any) => ({
          id: img.node.id,
          altText: img.node.altText || "",
        })) || [],
      }));

      // Perform comprehensive SEO optimization
      const geminiService = new GeminiService(apiKey);
      const optimizationResults = await geminiService.comprehensiveSeoOptimization(products, {
        updateProductTitle: settings.updateProductTitle || false,
        updateProductDescription: settings.updateProductDescription || false,
        updateSeoFields: settings.updateSeoFields || true,
        updateHandle: settings.updateHandle || false,
        updateImageAlts: settings.updateImageAlts || false,
      });

      // If auto-apply is enabled, apply changes immediately
      if (settings.autoApply) {
        const mutations = optimizationResults
          .filter(result => !result.error)
          .map(async (result) => {
            const mutations = [];

            // Update product fields if requested
            if (settings.updateProductTitle || settings.updateProductDescription || settings.updateHandle) {
              const productInput: any = {
                id: `gid://shopify/Product/${result.productId}`,
              };

              if (settings.updateProductTitle && result.optimizedProductTitle) {
                productInput.title = result.optimizedProductTitle;
              }

              if (settings.updateProductDescription && result.optimizedProductDescription) {
                productInput.descriptionHtml = result.optimizedProductDescription;
              }

              if (settings.updateHandle && result.optimizedHandle) {
                productInput.handle = result.optimizedHandle;
              }

              mutations.push(
                admin.graphql(`
                  mutation updateProduct($input: ProductInput!) {
                    productUpdate(input: $input) {
                      product {
                        id
                        title
                        description
                        handle
                      }
                      userErrors {
                        field
                        message
                      }
                    }
                  }
                `, {
                  variables: {
                    input: productInput,
                  },
                })
              );
            }

            // Update SEO fields
            if (settings.updateSeoFields && (result.optimizedSeoTitle || result.optimizedSeoDescription)) {
              const seoInput: any = {};

              if (result.optimizedSeoTitle) {
                seoInput.title = result.optimizedSeoTitle;
              }

              if (result.optimizedSeoDescription) {
                seoInput.description = result.optimizedSeoDescription;
              }

              mutations.push(
                admin.graphql(`
                  mutation updateProductSeo($input: ProductInput!) {
                    productUpdate(input: $input) {
                      product {
                        id
                        seo {
                          title
                          description
                        }
                      }
                      userErrors {
                        field
                        message
                      }
                    }
                  }
                `, {
                  variables: {
                    input: {
                      id: `gid://shopify/Product/${result.productId}`,
                      seo: seoInput,
                    },
                  },
                })
              );
            }

            // Update image alt texts if requested - use SEO title as alt text
            if (settings.updateImageAlts && result.optimizedSeoTitle) {
              // Find the corresponding product data to get image IDs
              const productData = products.find(p => p.id === result.productId);
              if (productData && productData.images) {
                // Update each image with the SEO title as alt text
                productData.images.forEach((image: any) => {
                  mutations.push(
                    admin.graphql(`
                      mutation updateProductImage($productId: ID!, $image: ProductImageInput!) {
                        productImageUpdate(productId: $productId, image: $image) {
                          image {
                            id
                            altText
                          }
                          userErrors {
                            field
                            message
                          }
                        }
                      }
                    `, {
                      variables: {
                        productId: `gid://shopify/Product/${result.productId}`,
                        image: {
                          id: image.id,
                          altText: result.optimizedSeoTitle,
                        },
                      },
                    })
                  );
                });
              }
            }

            return Promise.all(mutations);
          });

        // Execute mutations with error handling
        const mutationResults = await Promise.allSettled(mutations);

        // Log any mutation failures but don't crash the entire process
        mutationResults.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`❌ Mutation ${index + 1} failed:`, result.reason);
          } else {
            console.log(`✅ Mutation ${index + 1} completed successfully`);
          }
        });

        const successCount = optimizationResults.filter(r => !r.error).length;
        const errorCount = optimizationResults.filter(r => r.error).length;
        const mutationSuccessCount = mutationResults.filter(r => r.status === 'fulfilled').length;
        const mutationErrorCount = mutationResults.filter(r => r.status === 'rejected').length;

        console.log(`📊 Optimization Summary: ${successCount} optimized, ${errorCount} failed`);
        console.log(`📊 Mutation Summary: ${mutationSuccessCount} applied, ${mutationErrorCount} failed`);

        return json({
          success: true,
          message: `🎉 Successfully optimized ${successCount} products with viral keywords and comprehensive SEO improvements!`,
          results: optimizationResults.map(result => ({
            ...result,
            improvements: {
              titleOptimized: result.optimizedProductTitle !== result.originalProductTitle,
              descriptionOptimized: result.optimizedProductDescription !== result.originalProductDescription,
              seoFieldsOptimized: result.optimizedSeoTitle || result.optimizedSeoDescription,
              viralKeywordsAdded: result.viralKeyword ? true : false,
              seoScoreImprovement: result.seoScore || 0
            }
          })),
          summary: {
            totalProcessed: optimizationResults.length,
            successCount,
            errorCount,
            averageImprovementScore: successCount > 0
              ? Math.round(optimizationResults
                  .filter(r => !r.error && r.seoScore)
                  .reduce((sum, r) => sum + (r.seoScore || 0), 0) / successCount)
              : 0,
            topViralKeywords: [...new Set(optimizationResults
              .filter(r => !r.error && r.viralKeyword)
              .map(r => r.viralKeyword)
              .slice(0, 5))]
          },
          autoApplied: true,
        });
      } else {
        return json({
          success: true,
          results: optimizationResults,
          autoApplied: false,
        });
      }
    } catch (error) {
      console.error('❌ Comprehensive SEO optimization failed:', error);
      activeRequests.delete(requestId);
      return json({
        error: `Optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }, { status: 500 });
    } finally {
      // Clean up the request tracking
      activeRequests.delete(requestId);
    }
    } catch (error) {
      console.error('❌ Action error:', error);
      return json({ error: "Internal server error" }, { status: 500 });
    }
  }

  if (action === "optimize_with_credits") {
    console.log('🔄 Processing optimize_with_credits action');
    const { session } = auth;
    const productIds = JSON.parse(formData.get("productIds")?.toString() || "[]");
    const settings = JSON.parse(formData.get("settings")?.toString() || "{}");

    console.log(`📊 Credit optimization request: ${productIds.length} products for shop ${session.shop}`);

    try {
      // Check if user has enough credits
      const creditsService = new CreditsService(session.shop);
      const creditBalance = await creditsService.getCreditBalance();

      if (creditBalance.remainingCredits < productIds.length) {
        return json({
          error: `Insufficient credits. Required: ${productIds.length}, Available: ${creditBalance.remainingCredits}`
        }, { status: 400 });
      }

      // Use credits for optimization
      const success = await creditsService.useCredits(
        productIds.length,
        `SEO optimization for ${productIds.length} products`,
        `batch_${Date.now()}`
      );

      if (!success) {
        return json({ error: "Failed to use credits" }, { status: 400 });
      }

      // Start the SEO optimization process
      console.log('🚀 Starting SEO optimization service...');
      const seoService = createSEOOptimizationService(admin, session.shop);
      console.log('✅ SEO service created, starting optimization...');
      const result = await seoService.startOptimization(productIds, settings, {
        action: 'optimize_with_credits',
        shop: session.shop
      });
      console.log('✅ SEO optimization started:', result);

      const response = {
        success: true,
        requestId: result.requestId, // Include the request ID for progress tracking
        message: `Successfully started optimization for ${productIds.length} products using credits`,
        creditsUsed: productIds.length,
        remainingCredits: creditBalance.remainingCredits - productIds.length
      };
      console.log('📤 Sending response:', response);
      return json(response);

    } catch (error) {
      console.error('❌ Credit optimization error:', error);
      return json({
        error: error instanceof Error ? error.message : "Failed to optimize with credits"
      }, { status: 500 });
    }
  }

  // Handle progress status check
  if (action === "check_progress") {
    try {
      const { admin, session } = auth;
      const requestId = formData.get("requestId")?.toString();
      if (!requestId) {
        return json({ error: "Request ID is required" }, { status: 400 });
      }

      // Apply rate limiting for progress checks
      await applyRateLimit(RATE_LIMITERS.SEO_PROGRESS(session.shop), {
        action: 'check_progress',
        shop: session.shop
      });

      const seoService = createSEOOptimizationService(admin, session.shop);
      const status = await seoService.getOptimizationStatus(requestId);

      return json({
        success: true,
        status: status
      });
    } catch (error) {
      console.error('❌ Progress check error:', error);
      return json({
        error: error instanceof Error ? error.message : "Failed to check progress"
      }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
});

export default function SeoOptimizationDashboard() {
  const loaderData = useLoaderData<typeof loader>();
  const { products, productTypes, totalProducts, billing } = loaderData;
  const initialCreditBalance = (loaderData as any).creditBalance;
  const csrfToken = (loaderData as any).csrfToken;

  // Local state for credit balance that can be updated
  const [creditBalance, setCreditBalance] = useState(initialCreditBalance);

  // Update local credit balance when loader data changes
  useEffect(() => {
    setCreditBalance(initialCreditBalance);
  }, [initialCreditBalance]);
  const fetcher = useFetcher();
  const progressFetcher = useFetcher();
  const navigate = useNavigate();
  const [shopify, setShopify] = useState<any>(null);

  // Initialize shopify only on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('@shopify/app-bridge-react').then(({ useAppBridge }) => {
        try {
          setShopify(useAppBridge());
        } catch (error) {
          console.warn('Failed to initialize app bridge:', error);
        }
      });
    }
  }, []);
  const progressSectionRef = useRef<HTMLDivElement>(null);

  // Helper function for safe toast usage
  const showToast = useCallback((message: string, options?: { isError?: boolean; duration?: number }) => {
    try {
      if (shopify?.toast) {
        shopify.toast.show(message, options);
      } else {
        console.log(`Toast: ${message}`, options);
      }
    } catch (error) {
      console.warn('Toast error:', error);
      console.log(`Toast: ${message}`, options);
    }
  }, [shopify]);

  // Billing hooks
  const payPerUseBilling = usePayPerUseBilling();

  // State management
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUsingCredits, setIsUsingCredits] = useState(false); // Flag to distinguish credit vs comprehensive flow
  const [requestId, setRequestId] = useState<string | null>(null);
  const [progressPollingInterval, setProgressPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [progressPollingErrors, setProgressPollingErrors] = useState(0);
  const [completionResults, setCompletionResults] = useState<any>(null);
  const [showResults, setShowResults] = useState(false);
  const [showPayPerUseModal, setShowPayPerUseModal] = useState(false);
  const [progressData, setProgressData] = useState<{
    currentBatch: number;
    totalBatches: number;
    currentProduct: string;
    completedProducts: number;
    totalProducts: number;
    stage: string;
    startTime: number;
  } | null>(null);

  // SEO optimization settings
  const [settings, setSettings] = useState({
    updateProductTitle: false,
    updateProductDescription: false,
    updateSeoFields: true,
    updateHandle: false,
    updateImageAlts: false,
    autoApply: true,
    batchSize: 10,
  });

  // Progress simulation effect (only for comprehensive optimization, not credit-based)
  useEffect(() => {
    if (isProcessing && progressData && !isUsingCredits) {
      const interval = setInterval(() => {
        setProgressData(prev => {
          if (!prev) return null;

          const elapsed = Date.now() - prev.startTime;
          const estimatedTotal = (prev.totalProducts * 3000); // ~3 seconds per product
          const estimatedProgress = Math.min(95, (elapsed / estimatedTotal) * 100);

          // Simulate batch progression
          const currentBatch = Math.floor(estimatedProgress / 100 * prev.totalBatches) + 1;
          const completedProducts = Math.floor(estimatedProgress / 100 * prev.totalProducts);

          return {
            ...prev,
            currentBatch: Math.min(currentBatch, prev.totalBatches),
            completedProducts: Math.min(completedProducts, prev.totalProducts),
            stage: currentBatch <= prev.totalBatches
              ? `Processing batch ${currentBatch}/${prev.totalBatches}...`
              : 'Finalizing optimizations...'
          };
        });
      }, 500);

      return () => clearInterval(interval);
    }
  }, [isProcessing, progressData, isUsingCredits]);

  // Handle fetcher response for comprehensive optimization (not credit optimization)
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data && !isUsingCredits) {
      const responseData = fetcher.data as any;

      // Check if we have results (even with some errors) - this is for comprehensive optimization
      if ((responseData.success || (responseData.results && responseData.results.length > 0)) && responseData.results) {
        const results = responseData.results || [];
        const successCount = results.filter((r: any) => !r.error).length;
        const errorCount = results.filter((r: any) => r.error).length;

        // Set completion results for beautiful display
        setCompletionResults({
          totalProcessed: results.length,
          successCount,
          errorCount,
          results: results,
          processingTime: Date.now() - (requestId ? parseInt(requestId) : Date.now()),
          timestamp: new Date().toLocaleString(),
          summary: responseData.summary || {},
          message: responseData.message || `Optimized ${successCount} products successfully!`
        });

        setShowResults(true);
        setIsProcessing(false);
        setProgressData(null); // Clear progress data

        // Show appropriate toast based on results
        if (successCount > 0) {
          if (errorCount > 0) {
            showToast(`✅ Optimized ${successCount} products successfully! ${errorCount} had issues.`, { duration: 5000 });
          } else {
            showToast(`🎉 Successfully optimized ${successCount} products!`, { duration: 5000 });
          }
        } else if (errorCount > 0) {
          showToast(`⚠️ Optimization completed with ${errorCount} errors. Check results for details.`, { isError: true, duration: 5000 });
        }

        // Clear selected products
        setSelectedProducts([]);

      } else if (responseData.error) {
        // Handle duplicate request differently - don't show as error
        if (responseData.isDuplicate) {
          showToast('Optimization already in progress. Please wait...', { duration: 3000 });
          // Don't change processing state or progress data for duplicates
          return;
        }

        // Show error but don't crash the UI
        console.error('❌ Optimization error:', responseData.error);
        showToast(`Optimization failed: ${responseData.error}`, { isError: true, duration: 5000 });
        setIsProcessing(false);

        // Keep progress data but mark as error state
        if (progressData) {
          setProgressData({
            ...progressData,
            stage: '❌ Error occurred during optimization',
            currentProduct: 'Process failed'
          });

          // Clear progress data after 5 seconds to let user see the error
          setTimeout(() => {
            setProgressData(null);
          }, 5000);
        }
      }
    }
  }, [fetcher.state, fetcher.data, shopify, requestId, isUsingCredits]);

  // Filtering logic
  const getFilteredProducts = useCallback(() => {
    return products.filter((product: any) => {
      const matchesSearch = !searchValue ||
        product.title.toLowerCase().includes(searchValue.toLowerCase()) ||
        product.description.toLowerCase().includes(searchValue.toLowerCase());

      const matchesType = typeFilter === 'all' || product.type === typeFilter;

      return matchesSearch && matchesType;
    });
  }, [products, searchValue, typeFilter]);

  // Product selection handlers
  const handleProductSelect = useCallback((productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    console.log('🔵🔵🔵 handleSelectAll called (BLUE Select All button) 🔵🔵🔵');
    console.log('🔵 This should select ALL products or deselect all');
    const filteredProducts = getFilteredProducts();

    // Simple select all / deselect all logic
    const allSelected = filteredProducts.every((p: any) => selectedProducts.includes(p.id));

    if (allSelected) {
      // All products are selected, so deselect all
      console.log('🔵 Deselecting all products');
      setSelectedProducts([]);
    } else {
      // Not all products are selected, so select all
      console.log('🔵 Selecting all products:', filteredProducts.length);
      setSelectedProducts(filteredProducts.map((p: any) => p.id));
    }
  }, [selectedProducts, getFilteredProducts]);

  const handleSelectNonOptimized = useCallback(() => {
    console.log('🟢🟢🟢 handleSelectNonOptimized called (GREEN Select Needs Optimization button) 🟢🟢🟢');
    console.log('🟢 This should ONLY select products with SEO score < 80 or status pending/failed');
    const filteredProducts = getFilteredProducts();

    // Debug: Show SEO score distribution
    const seoScores = filteredProducts.filter(p => p != null).map(p => p.seoScore).sort((a, b) => a - b);
    console.log('📊 SEO Score Distribution:', {
      total: filteredProducts.length,
      min: seoScores.length > 0 ? Math.min(...seoScores) : 0,
      max: seoScores.length > 0 ? Math.max(...seoScores) : 0,
      avg: seoScores.length > 0 ? Math.round(seoScores.reduce((a, b) => a + b, 0) / seoScores.length) : 0,
      below80: seoScores.filter(s => s < 80).length,
      below70: seoScores.filter(s => s < 70).length,
      scores: seoScores.slice(0, 20) // Show first 20 scores only
    });

    // Debug: Check a few sample products to understand the data structure
    console.log('🔍 Sample products (first 3):', filteredProducts.slice(0, 3).map(p => ({
      id: p?.id,
      title: p?.title?.substring(0, 20) + '...',
      status: p?.status,
      seoScore: p?.seoScore,
      seoScoreType: typeof p?.seoScore,
      needsOptimization: (p?.seoScore != null && p.seoScore < 80) // FIXED: Only check SEO score
    })));

    // Select products that need optimization (SEO score < 80 ONLY)
    const nonOptimizedProducts = filteredProducts.filter((p: any) => {
      if (!p) return false;

      // FIXED: Only consider SEO score, ignore status
      const needsOptimization = Number(p.seoScore) < 80;

      // Debug individual product filtering
      if (filteredProducts.indexOf(p) < 5) { // Only log first 5 for brevity
        console.log(`🔍 Product ${p.id}: status=${p.status}, seoScore=${p.seoScore} (${typeof p.seoScore}), needsOptimization=${needsOptimization} (ONLY checking score < 80)`);
      }

      return needsOptimization;
    });

    console.log('🎯 Products that need optimization:', nonOptimizedProducts.length, 'out of', filteredProducts.length);
    console.log('🎯 Should be selecting:', nonOptimizedProducts.length, 'products');

    // Show first few products that need optimization
    console.log('🎯 First 5 products needing optimization:', nonOptimizedProducts.slice(0, 5).filter(p => p != null).map(p => ({
      id: p.id,
      title: p.title?.substring(0, 30) + '...',
      status: p.status,
      seoScore: p.seoScore
    })));

    const allNonOptimizedSelected = nonOptimizedProducts.every((p: any) => selectedProducts.includes(p.id));

    if (allNonOptimizedSelected && nonOptimizedProducts.length > 0) {
      // Deselect all non-optimized products
      const nonOptimizedIds = nonOptimizedProducts.map((p: any) => p.id);
      console.log('🔄 Deselecting products that need optimization:', nonOptimizedIds.length);
      setSelectedProducts(prev => prev.filter(id => !nonOptimizedIds.includes(id)));
    } else {
      // Select only non-optimized products (replace current selection)
      const nonOptimizedIds = nonOptimizedProducts.map((p: any) => p.id);
      console.log('✅ Selecting products that need optimization:', nonOptimizedIds.length);
      console.log('✅ Product IDs being selected:', nonOptimizedIds.slice(0, 10)); // Show first 10 IDs
      console.log('✅ About to call setSelectedProducts with', nonOptimizedIds.length, 'product IDs');
      setSelectedProducts(nonOptimizedIds);
      console.log('✅ setSelectedProducts called successfully');
    }
  }, [selectedProducts, getFilteredProducts]);

  // Handle optimization using existing credits
  const handleOptimizeWithCredits = useCallback(() => {
    console.log('🎯 handleOptimizeWithCredits called with:', {
      selectedProductsLength: selectedProducts.length,
      isProcessing,
      isUsingCredits
    });

    if (selectedProducts.length === 0) {
      showToast('Please select products to optimize', { isError: true });
      return;
    }

    setIsProcessing(true);
    setIsUsingCredits(true); // Flag this as credit-based optimization

    // Set progress data to show processing UI
    setProgressData({
      startTime: Date.now(),
      totalProducts: selectedProducts.length,
      completedProducts: 0,
      currentBatch: 1,
      totalBatches: 1,
      currentProduct: 'Initializing...',
      stage: 'Starting optimization with credits...'
    });

    // Use Remix fetcher for proper form submission
    const formData = new FormData();
    formData.append('action', 'optimize_with_credits');
    formData.append('productIds', JSON.stringify(selectedProducts));
    formData.append('settings', JSON.stringify(settings));
    formData.append('csrfToken', csrfToken);

    console.log('📤 About to submit form with data:', {
      action: 'optimize_with_credits',
      productIds: selectedProducts,
      settings,
      csrfToken
    });
    console.log('📤 FormData entries:', Array.from(formData.entries()));
    console.log('📤 Fetcher state before submit:', fetcher.state);

    fetcher.submit(formData, { method: 'POST', action: '/app/seo-dashboard' });

    console.log('📤 Fetcher state after submit:', fetcher.state);

    // Auto-scroll to processing container after a brief delay
    setTimeout(() => {
      if (progressSectionRef.current) {
        progressSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 100);

    // Progress polling will start when we get the requestId from the response
  }, [selectedProducts, settings, csrfToken, showToast, fetcher]);

  // Function to start polling for real progress updates
  const startProgressPolling = useCallback((optimizationRequestId: string) => {
    console.log(`🔄 Starting progress polling for request: ${optimizationRequestId}`);

    // Clear any existing interval first
    if (progressPollingInterval) {
      clearInterval(progressPollingInterval);
    }

    // Reset error counter when starting new polling
    setProgressPollingErrors(0);

    const interval = setInterval(() => {
      try {
        // Only poll if progressFetcher is not already submitting
        if (progressFetcher.state === 'idle') {
          const formData = new FormData();
          formData.append('action', 'check_progress');
          formData.append('requestId', optimizationRequestId);

          console.log(`🔄 Polling progress for request: ${optimizationRequestId} (errors: ${progressPollingErrors})`);
          progressFetcher.submit(formData, { method: 'POST', action: '/app/seo-dashboard' });
        } else {
          console.log(`⏳ Progress fetcher busy (${progressFetcher.state}), skipping poll`);
        }
      } catch (error) {
        console.error('❌ Progress polling error:', error);
        setProgressPollingErrors(prev => prev + 1);

        if (progressPollingErrors >= 5) {
          console.error(`❌ Too many polling errors (${progressPollingErrors}), stopping`);
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }
          showToast('Progress tracking failed. The optimization is still running in the background.', { isError: true });
        }
      }
    }, 5000); // Increased to 5 seconds to reduce server load

    setProgressPollingInterval(interval);
  }, [progressPollingInterval, progressFetcher, showToast, progressPollingErrors, setProgressPollingErrors]);

  // Handle progress fetcher response
  useEffect(() => {
    if (progressFetcher.state === 'idle' && progressFetcher.data) {
      const result = progressFetcher.data as any;
      console.log(`📊 Progress polling response:`, result);

      if (result.success && result.status) {
        const status = result.status;
        console.log(`📊 Progress update:`, status);

        // Reset error counter on successful response
        setProgressPollingErrors(0);

        // Update progress data with real data from server
        setProgressData(prev => prev ? {
          ...prev,
          currentBatch: status.progress.currentBatch,
          totalBatches: status.progress.totalBatches,
          completedProducts: status.progress.processedProducts,
          currentProduct: `Processing batch ${status.progress.currentBatch}/${status.progress.totalBatches}`,
          stage: status.status === 'processing' ? 'Optimizing products...' : status.status
        } : null);

        // If completed or failed, stop polling
        if (status.status === 'completed' || status.status === 'failed') {
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }

          if (status.status === 'completed') {
            setProgressData(prev => prev ? {
              ...prev,
              completedProducts: prev.totalProducts,
              currentProduct: `Successfully optimized ${prev.totalProducts} products!`,
              stage: 'Optimization completed successfully!'
            } : null);

            setTimeout(() => {
              showToast(`Successfully optimized ${selectedProducts.length} products!`);
              setIsProcessing(false);
              setIsUsingCredits(false);
              setProgressData(null);
              // Don't reload the page to avoid rate limit issues
              // window.location.reload();
            }, 2000);
          } else {
            showToast(`Optimization failed: ${status.error || 'Unknown error'}`, { isError: true });
            setIsProcessing(false);
            setIsUsingCredits(false);
            setProgressData(null);
          }
        }
      } else if (result.error) {
        console.error('❌ Progress polling error response:', result.error);
        setProgressPollingErrors(prev => prev + 1);

        // Don't show error toast for every failed poll, just log it
        // Only stop polling if it's a critical error or too many errors
        if (result.error.includes('502') || result.error.includes('Bad Gateway')) {
          console.log('🔄 Network error detected, continuing to poll...');
          // Continue polling, don't stop on network errors immediately
        }

        if (progressPollingErrors >= 5) {
          console.error(`❌ Too many polling errors (${progressPollingErrors}), stopping`);
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }
          showToast('Progress tracking failed due to network issues. The optimization is still running.', { isError: true });
        }
      }
    }

    // Handle network errors (when progressFetcher.data is null but there was an error)
    if (progressFetcher.state === 'idle' && !progressFetcher.data && isProcessing && isUsingCredits) {
      console.log('⚠️ Progress fetcher returned no data, likely a network error');
      // Don't stop the process, just continue polling
    }
  }, [progressFetcher.state, progressFetcher.data, progressPollingInterval, showToast, selectedProducts.length, isProcessing, isUsingCredits]);

  // Update progress based on fetcher state
  useEffect(() => {
    if (!isUsingCredits) return;

    if (fetcher.state === 'submitting') {
      setProgressData(prev => prev ? {
        ...prev,
        stage: 'Processing your request...',
        currentProduct: 'Validating credits and permissions...'
      } : null);
    } else if (fetcher.state === 'loading') {
      setProgressData(prev => prev ? {
        ...prev,
        stage: 'Optimizing products...',
        currentProduct: `Processing ${prev.totalProducts} selected products...`,
        completedProducts: Math.floor(prev.totalProducts * 0.3)
      } : null);
    } else if (fetcher.state === 'idle' && fetcher.data) {
      // When fetcher becomes idle with data, it means the request completed
      // The credit optimization handler will take over from here
      setProgressData(prev => prev ? {
        ...prev,
        stage: 'Request completed, starting optimization...',
        currentProduct: 'Initializing optimization process...'
      } : null);
    }
  }, [fetcher.state, isUsingCredits, fetcher.data]);

  // Handle fetcher response for credit optimization ONLY
  useEffect(() => {
    console.log(`🔄 Fetcher state changed: ${fetcher.state}`, {
      data: fetcher.data,
      isUsingCredits,
      hasData: !!fetcher.data,
      dataKeys: fetcher.data ? Object.keys(fetcher.data) : []
    });
    if (fetcher.state === 'idle' && fetcher.data && isUsingCredits) {
      const data = fetcher.data as any;
      console.log(`📊 Credit optimization response data:`, data);
      if (data.success && data.requestId) {
        console.log(`✅ Optimization started with requestId: ${data.requestId}`);
        setRequestId(data.requestId);

        // Update credit balance with the new remaining credits
        if (data.remainingCredits !== undefined) {
          setCreditBalance((prev: any) => ({
            ...prev,
            remainingCredits: data.remainingCredits,
            usedCredits: prev.usedCredits + (data.creditsUsed || 0)
          }));
          console.log(`💰 Updated credit balance: ${data.remainingCredits} remaining`);
        }

        // Start polling for real progress updates
        startProgressPolling(data.requestId);

        // Update progress to show that polling has started
        setProgressData(prev => prev ? {
          ...prev,
          currentProduct: 'Connecting to optimization process...',
          stage: 'Starting real-time progress tracking...'
        } : null);

      } else if (data.error) {
        showToast(data.error, { isError: true });
        setIsProcessing(false);
        setIsUsingCredits(false);
        setProgressData(null);
      }
    } else if (fetcher.state === 'idle' && isProcessing && !fetcher.data) {
      // Reset processing state if fetcher is idle but no data (likely an error)
      setIsProcessing(false);
      setIsUsingCredits(false);
      setProgressData(null);
    }
  }, [fetcher.state, fetcher.data, selectedProducts.length, showToast, isProcessing, isUsingCredits]);

  // Cleanup polling interval on unmount
  useEffect(() => {
    return () => {
      if (progressPollingInterval) {
        clearInterval(progressPollingInterval);
      }
    };
  }, [progressPollingInterval]);

  // Start comprehensive SEO optimization
  const handleStartOptimization = useCallback(() => {
    if (selectedProducts.length === 0) {
      showToast('Please select products to optimize', { isError: true });
      return;
    }

    // Prevent duplicate requests
    if (isProcessing) {
      console.log('⚠️ Already processing - please wait');
      return;
    }

    // Check billing access and determine payment method
    const billingData = billing as any;
    console.log('🔍 Billing check - hasAccess:', billing.hasAccess);
    console.log('🔍 Billing check - plan:', billingData.plan);
    console.log('🔍 Billing check - subscription:', billingData.subscription);


    // If user has no plan at all, show pay-per-use modal (default to pay-per-use)
    if (!billingData.plan) {
      console.log('💳 No plan selected, showing pay-per-use modal as default');
      setShowPayPerUseModal(true);
      return;
    }

    // If user has pay-per-use plan, check if they have enough credits
    if (billingData.plan.id === 'pay_per_use') {
      console.log('💳 Pay-per-use plan detected, checking credit balance...');
      console.log('💰 Credit balance:', creditBalance);
      console.log('💰 Credit balance details:', {
        remainingCredits: creditBalance?.remainingCredits,
        totalCredits: creditBalance?.totalCredits,
        usedCredits: creditBalance?.usedCredits,
        selectedProductsLength: selectedProducts.length
      });

      if (creditBalance && creditBalance.remainingCredits >= selectedProducts.length) {
        console.log('✅ Sufficient credits available, proceeding with optimization');
        console.log('🎯 About to call handleOptimizeWithCredits...');
        // User has enough credits, proceed with optimization
        handleOptimizeWithCredits();
        console.log('🎯 handleOptimizeWithCredits call completed');
        return;
      } else {
        console.log('❌ Insufficient credits, showing payment modal');
        console.log(`Required: ${selectedProducts.length}, Available: ${creditBalance?.remainingCredits || 0}`);
        setShowPayPerUseModal(true);
        return;
      }
    }

    // If user has subscription plan but no active subscription, show pay-per-use modal
    if (!billingData.subscription || billingData.subscription.status !== 'ACTIVE') {
      console.log('💳 No active subscription, showing pay-per-use modal');
      setShowPayPerUseModal(true);
      return;
    }



    console.log('✅ User has active subscription, proceeding with optimization');

    const newRequestId = Date.now().toString();
    if (requestId && Date.now() - parseInt(requestId) < 10000) {
      console.log('⚠️ Preventing duplicate request - please wait 10 seconds between requests');
      showToast('Please wait 10 seconds between optimization requests', { isError: true });
      return;
    }

    setRequestId(newRequestId);
    setIsProcessing(true);

    // Initialize progress tracking
    const totalProducts = selectedProducts.length;
    const totalBatches = Math.ceil(totalProducts / 10); // 10 products per batch
    setProgressData({
      currentBatch: 0,
      totalBatches,
      currentProduct: '',
      completedProducts: 0,
      totalProducts,
      stage: 'Initializing...',
      startTime: Date.now()
    });

    // Scroll to progress section
    setTimeout(() => {
      if (progressSectionRef.current) {
        progressSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }, 100); // Small delay to ensure the progress section is rendered

    const formData = new FormData();
    formData.append('action', 'optimize_comprehensive');
    formData.append('productIds', JSON.stringify(selectedProducts));
    formData.append('settings', JSON.stringify(settings));
    formData.append('requestId', newRequestId);

    fetcher.submit(formData, { method: 'POST' });
  }, [selectedProducts, settings, fetcher, shopify, navigate, isProcessing, requestId, billing]);

  // Handle pay-per-use confirmation
  const handlePayPerUseConfirm = useCallback(() => {
    setShowPayPerUseModal(false);
    // The PayPerUseConfirmation component will handle the payment flow
    // and redirect to Shopify for payment confirmation
  }, []);

  // Handle pay-per-use cancellation
  const handlePayPerUseCancel = useCallback(() => {
    setShowPayPerUseModal(false);
  }, []);

  // Calculate dashboard metrics
  const dashboardMetrics = {
    totalProducts: totalProducts,
    selectedCount: selectedProducts.length,
    averageSeoScore: products.length > 0
      ? Math.round(products.reduce((sum: number, p: any) => sum + (p?.seoScore || 0), 0) / products.length)
      : 0,
    needsOptimization: products.filter((p: any) => p?.seoScore < 80).length,
    optimizedCount: products.filter((p: any) => p?.status === 'optimized').length,
  };

  const filteredProducts = getFilteredProducts();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="min-h-screen bg-black text-white"
    >
      <TitleBar title="AI BULK SEO - SEO Optimizer" />

      <SeoOptimizationHeader
        totalProducts={dashboardMetrics.totalProducts}
        selectedCount={selectedProducts.length}
        averageSeoScore={dashboardMetrics.averageSeoScore}
        onOptimizeSelected={handleStartOptimization}
        onSelectAll={handleSelectAll}
        isOptimizing={isProcessing}
      />

      {/* Content Container */}
      <div className="bg-black py-20 px-6">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          <motion.div
            className="flex flex-col gap-8"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {/* Metrics Grid */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              {/* Total Products Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <div className="bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all">
                  <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="text-sm font-medium text-white/70">
                      Total Products
                    </div>
                    <div className="w-8 h-8 bg-white/20 rounded-2xl flex items-center justify-center">
                      <MorphingShapeAnimation size={16} />
                    </div>
                  </div>
                  <div className="pt-2">
                    <div className="text-2xl font-bold text-white">
                      {dashboardMetrics.totalProducts.toLocaleString()}
                    </div>
                    <p className="text-xs text-white/70">
                      Ready for optimization
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Average SEO Score Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <div className="bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all">
                  <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="text-sm font-medium text-white/70">
                      Average SEO Score
                    </div>
                    <div className={cn(
                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                      dashboardMetrics.averageSeoScore >= 80 ? "bg-white/30" : "bg-white/20"
                    )}>
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        dashboardMetrics.averageSeoScore >= 80 ? "bg-white" : "bg-white/70"
                      )} />
                    </div>
                  </div>
                  <div className="pt-2">
                    <div className="text-2xl font-bold text-white">
                      {dashboardMetrics.averageSeoScore}%
                    </div>
                    <p className="text-xs text-white/70">
                      {dashboardMetrics.averageSeoScore >= 80 ? 'Excellent performance' :
                       dashboardMetrics.averageSeoScore >= 60 ? 'Good, can improve' : 'Needs attention'}
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Needs Optimization Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <div className="bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all">
                  <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="text-sm font-medium text-white/70">
                      Need Optimization
                    </div>
                    <div className={cn(
                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                      dashboardMetrics.needsOptimization > 0 ? "bg-white/30" : "bg-white/20"
                    )}>
                      <div className={cn(
                        "w-2 h-2 rotate-45",
                        dashboardMetrics.needsOptimization > 0 ? "bg-white" : "bg-white/70"
                      )} />
                    </div>
                  </div>
                  <div className="pt-2">
                    <div className="text-2xl font-bold text-white">
                      {dashboardMetrics.needsOptimization.toLocaleString()}
                    </div>
                    <p className="text-xs text-white/70">
                      Products below 80% score
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Optimized Count Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
              >
                <div className="bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all">
                  <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="text-sm font-medium text-white/70">
                      Optimized
                    </div>
                    <div className="w-8 h-8 bg-green-500/30 rounded-2xl flex items-center justify-center">
                      <SuccessAnimation size={16} />
                    </div>
                  </div>
                  <div className="pt-2">
                    <div className="text-2xl font-bold text-white">
                      {dashboardMetrics.optimizedCount.toLocaleString()}
                    </div>
                    <p className="text-xs text-white/70">
                      Successfully optimized
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Credits Remaining Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                <div className="bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all">
                  <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div className="text-sm font-medium text-white/70">
                      Credits Remaining
                    </div>
                    <div className={cn(
                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                      creditBalance?.remainingCredits > 0 ? "bg-green-500/30" : "bg-red-500/30"
                    )}>
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        creditBalance?.remainingCredits > 0 ? "bg-green-400" : "bg-red-400"
                      )} />
                    </div>
                  </div>
                  <div className="pt-2">
                    <div className="text-2xl font-bold text-white">
                      {creditBalance?.remainingCredits || 0}
                    </div>
                    <p className="text-xs text-white/70">
                      {creditBalance?.remainingCredits > 0
                        ? `${creditBalance.usedCredits}/${creditBalance.totalCredits} used`
                        : 'Purchase credits to optimize'
                      }
                    </p>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Billing Status */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <BillingStatus
                subscription={(billing as any).subscription as SubscriptionData | undefined}
                plan={(billing as any).plan}
                hasAccess={billing.hasAccess}
                monthlyUsage={{
                  productsOptimized: dashboardMetrics.optimizedCount,
                  totalSpent: (billing as any).plan?.type === 'pay_per_use'
                    ? dashboardMetrics.optimizedCount * 0.10
                    : (billing as any).plan?.price || 0
                }}
              />
            </motion.div>

            {/* Pro Tip Banner */}
            <div className="bg-yellow-500/20 border border-yellow-500/40 rounded-3xl p-6">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-yellow-400 rounded-2xl flex items-center justify-center flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-black rounded-full" />
                </div>
                <div>
                  <div className="text-sm font-medium text-yellow-300 mb-1">
                    Pro Tip
                  </div>
                  <div className="text-sm text-yellow-200 leading-relaxed">
                    Run these audits on your store's main pages to identify SEO opportunities before optimizing your products.
                    Both tools are completely free and provide actionable insights.
                  </div>
                </div>
              </div>
            </div>
        </motion.div>

        {/* Optimization Settings */}
        <motion.div
          className="bg-white/10 border border-white/20 rounded-3xl p-8 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2 text-white">Optimization Settings</h2>
            <p className="text-white/70">
              Configure which fields to optimize and how to process your products
            </p>
          </div>
          <motion.div
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {/* Fields to Update */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <h3 className="text-base font-semibold mb-4 text-white">
                Fields to Update
              </h3>

              <div className="space-y-3">
                  {[
                    { key: 'productTitle', label: 'Product Title', desc: 'Optimize main product titles' },
                    { key: 'productDescription', label: 'Product Description', desc: 'Enhance product descriptions' },
                    { key: 'seoFields', label: 'SEO Meta Tags', desc: 'Page title & meta description' },
                    { key: 'handle', label: 'URL Handle', desc: 'SEO-friendly URLs' },
                    { key: 'imageAlts', label: 'Image Alt Texts', desc: 'Accessibility & SEO' }
                  ].map((field) => (
                    <label
                      key={field.key}
                      className={cn(
                        "flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",
                        settings[`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}` as keyof typeof settings]
                          ? "border-white/40 bg-white/10" : "border-white/20 bg-white/5 hover:bg-white/10"
                      )}
                    >
                      <input
                        type="checkbox"
                        checked={settings[`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}` as keyof typeof settings] as boolean}
                        onChange={(e) => {
                          setSettings(prev => ({
                            ...prev,
                            [`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}`]: e.target.checked
                          }));
                        }}
                        className="w-4 h-4 accent-white"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-white">
                          {field.label}
                        </div>
                        <div className="text-xs text-white/70">
                          {field.desc}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </motion.div>

              {/* Processing Options */}
              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                <h3 className="text-base font-semibold mb-4 text-white">
                  Processing Options
                </h3>

                <div className="space-y-6">
                  {/* Auto Apply Setting */}
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium mb-1 text-white">
                        Application Mode
                      </div>
                      <div className="text-xs text-white/70">
                        Choose how to apply optimizations
                      </div>
                    </div>

                    <div className="space-y-3">
                      {[
                        { value: true, label: 'Auto-apply changes immediately', desc: 'Faster processing' },
                        { value: false, label: 'Review before applying', desc: 'Manual approval' }
                      ].map((option) => (
                        <label
                          key={option.value.toString()}
                          className={cn(
                            "flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",
                            settings.autoApply === option.value
                              ? "border-white/40 bg-white/10" : "border-white/20 bg-white/5 hover:bg-white/10"
                          )}
                        >
                          <input
                            type="radio"
                            name="autoApply"
                            checked={settings.autoApply === option.value}
                            onChange={() => {
                              setSettings(prev => ({
                                ...prev,
                                autoApply: option.value
                              }));
                            }}
                            className="w-4 h-4 accent-white"
                          />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-white">
                              {option.label}
                            </div>
                            <div className="text-xs text-white/70">
                              {option.desc}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Batch Size Setting */}
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium mb-1 text-white">
                        Batch Size
                      </div>
                      <div className="text-xs text-white/70">
                        Products processed per batch
                      </div>
                    </div>

                    <select
                      value={settings.batchSize}
                      onChange={(e) => {
                        setSettings(prev => ({
                          ...prev,
                          batchSize: parseInt(e.target.value)
                        }));
                      }}
                      className="w-full p-4 rounded-2xl border border-white/20 bg-white/5 text-white text-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/40 hover:bg-white/10"
                    >
                      <option value={5} className="bg-black text-white">5 products (Slower, more stable)</option>
                      <option value={10} className="bg-black text-white">10 products (Recommended)</option>
                      <option value={20} className="bg-black text-white">20 products (Faster, higher load)</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Processing Container */}
        <AnimatePresence>
          {isProcessing && progressData && (
            <motion.div
              ref={progressSectionRef}
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
            >
              <div className="bg-white/10 border border-white/20 rounded-3xl p-8 mb-8 relative overflow-hidden">
                {/* Floating Background Animation - Temporarily disabled */}
                <div className="absolute inset-0 opacity-10 pointer-events-none">
                  {/* <FloatingElementsAnimation size={300} /> */}
                </div>
                <div className="relative z-10">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <motion.div
                      className="w-20 h-20 bg-white/20 rounded-full mx-auto mb-5 flex items-center justify-center border border-white/30 shadow-lg"
                      whileHover={{ scale: 1.05 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      {/* <ProcessingAnimation size={64} /> */}
                      <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" />
                    </motion.div>
                    <h2 className="text-2xl font-bold mb-2 text-white">
                      SEO Optimization in Progress
                    </h2>
                    <p className="text-sm text-white/70">
                      {progressData.stage}
                    </p>
                  </div>

            {/* Progress Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
                  <div className="text-3xl font-bold mb-1 text-white">
                    {progressData.completedProducts}
                  </div>
                  <div className="text-xs text-white/70 font-medium uppercase tracking-wider">
                    Products Completed
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
                  <div className="text-3xl font-bold mb-1 text-white">
                    {progressData.totalProducts}
                  </div>
                  <div className="text-xs text-white/70 font-medium uppercase tracking-wider">
                    Total Products
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
                  <div className="text-3xl font-bold mb-1 text-white">
                    {Math.round(((Date.now() - progressData.startTime) / 1000))}s
                  </div>
                  <div className="text-xs text-white/70 font-medium uppercase tracking-wider">
                    Elapsed Time
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
                  <div className="text-3xl font-bold mb-1 text-white">
                    {progressData.completedProducts > 0
                      ? Math.round(((progressData.totalProducts - progressData.completedProducts) *
                          ((Date.now() - progressData.startTime) / progressData.completedProducts)) / 1000)
                      : Math.round(progressData.totalProducts * 2.5)}s
                  </div>
                  <div className="text-xs text-white/70 font-medium uppercase tracking-wider">
                    Est. Remaining
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <Progress
                value={Math.round((progressData.completedProducts / progressData.totalProducts) * 100)}
                className="h-3"
              />
            </div>

            {/* Current Product */}
            {progressData.currentProduct && (
              <div className="bg-white/5 border border-white/20 rounded-2xl p-6 mb-5 text-center">
                <div className="text-xs text-white/70 mb-2 font-medium uppercase tracking-wider">
                  Currently Processing:
                </div>
                <div className="text-base font-semibold truncate text-white">
                  {progressData.currentProduct}
                </div>
              </div>
            )}

            {/* Processing Tips */}
            <div className="bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
              <div className="text-sm text-white/70 italic">
                💡 Keep this tab open for the best experience. Processing typically takes 2-3 seconds per product.
              </div>
            </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Product List Section */}
        {!isProcessing && (
          <div className="bg-white/10 border border-white/20 rounded-3xl p-8 mb-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-2 text-white">Product Selection</h2>
              <p className="text-white/70">
                Select products to optimize their SEO titles, descriptions, and viral keywords.
              </p>
            </div>
            <div>

              {/* Search and Filter Controls */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex gap-4 mb-6 flex-wrap"
              >
                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  className="flex-1 min-w-[300px]"
                >
                  <Input
                    type="text"
                    placeholder="Search products..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                  />
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="min-w-[200px]"
                >
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="bg-white/5 border-white/20 text-white focus:border-white/40">
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent className="bg-black border-white/20">
                      <SelectItem value="all" className="text-white hover:bg-white/10">All Types</SelectItem>
                      {productTypes.filter(type => type != null).map(type => (
                        <SelectItem key={type} value={type} className="text-white hover:bg-white/10">{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </motion.div>
              </motion.div>

          {/* Selection Controls */}
          <div className="flex justify-between items-center mb-4 py-3 border-b border-white/20">
            <div className="flex items-center gap-3">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <ShadcnButton
                  onClick={handleSelectAll}
                  size="sm"
                  className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 bg-transparent border-blue-400/50"
                >
                  {(() => {
                    const allSelected = filteredProducts.every((p: any) => selectedProducts.includes(p.id));
                    return allSelected ? 'Deselect All' : 'Select All';
                  })()}
                </ShadcnButton>
              </motion.div>
              <Separator orientation="vertical" className="h-4 bg-white/20" />
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <ShadcnButton
                  onClick={handleSelectNonOptimized}
                  size="sm"
                  className="text-green-400 hover:text-green-300 hover:bg-green-500/20 bg-transparent border-green-400/50"
                >
                  {(() => {
                    const nonOptimizedProducts = filteredProducts.filter((p: any) =>
                      p.seoScore < 80
                    );
                    const allNonOptimizedSelected = nonOptimizedProducts.every((p: any) => selectedProducts.includes(p.id));
                    return allNonOptimizedSelected && nonOptimizedProducts.length > 0 ? 'Deselect Needs Optimization' : 'Select Needs Optimization';
                  })()}
                </ShadcnButton>
              </motion.div>
              <span className="text-sm text-white/70">
                {selectedProducts.length} of {filteredProducts.length} selected
                {(() => {
                  const needsOptimizationCount = filteredProducts.filter((p: any) =>
                    p.seoScore < 80
                  ).length;
                  return needsOptimizationCount > 0 ? ` (${needsOptimizationCount} need optimization)` : '';
                })()}
              </span>
            </div>
            <div className="text-sm text-white/70">
              Showing {filteredProducts.length} products
            </div>
          </div>

              {/* Product Table */}
              <div className="border border-white/20 rounded-2xl overflow-hidden">
                <div className="bg-white/5 px-4 py-3 border-b border-white/20 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center text-xs font-semibold text-white/70 uppercase tracking-wider min-w-[600px]">
                  <div></div>
                  <div>Product</div>
                  <div>Type</div>
                  <div>SEO Score</div>
                  <div>Status</div>
                </div>

                {/* Scrollable Product List Container */}
                <div className="max-h-96 overflow-y-auto overflow-x-auto">
                  {filteredProducts.length === 0 ? (
                    <div className="py-12 px-4 text-center text-white/70">
                      <div className="text-base mb-2">No products found</div>
                      <div className="text-sm">Try adjusting your search or filter criteria</div>
                    </div>
                  ) : (
                    filteredProducts.map((product: any, index: number) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.3,
                        delay: index * 0.05,
                        ease: "easeOut"
                      }}
                      whileHover={{
                        scale: 1.01,
                        transition: { duration: 0.2 }
                      }}
                      className="p-4 border-b border-white/10 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center cursor-pointer min-w-[600px] rounded-sm hover:bg-white/5"
                      onClick={() => handleProductSelect(product.id)}
                    >
                      <div>
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={() => handleProductSelect(product.id)}
                          className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
                        />
                      </div>
                      <div className="min-w-0 overflow-hidden">
                        <div className="text-sm font-medium mb-1 truncate text-white">
                          {product.title}
                        </div>
                        <div className="text-xs text-white/70 truncate">
                          {product.description || 'No description'}
                        </div>
                      </div>
                      <div className="text-xs text-white/70 truncate min-w-0">
                        {product.type}
                      </div>
                      <div className="min-w-0 overflow-hidden">
                        <Badge
                          variant={product.seoScore >= 80 ? "default" as const : product.seoScore >= 60 ? "secondary" as const : "destructive" as const}
                          className="text-xs"
                        >
                          {product.seoScore}%
                        </Badge>
                      </div>
                      <div className="min-w-0 overflow-hidden">
                        <Badge
                          variant={
                            product.status === 'optimized' ? "default" as const :
                            product.status === 'processing' ? "secondary" as const : "outline" as const
                          }
                          className="text-xs"
                        >
                          {product.status === 'pending' ? 'Ready' :
                           product.status === 'processing' ? 'Processing' :
                           product.status === 'optimized' ? 'Optimized' : 'Failed'}
                        </Badge>
                      </div>
                    </motion.div>
                  ))
                )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
          
        
      

      {/* Completion Results */}
    {showResults && completionResults ? (
      <div className="bg-black py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white/10 border border-white/20 rounded-3xl p-8 mb-8">
            <div className="pt-2">
              {/* Success Header */}
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-white rounded-full mx-auto mb-5 flex items-center justify-center">
                  <div className="text-2xl text-black">
                    ✓
                  </div>
                </div>
                <h1 className="text-3xl font-bold mb-2 tracking-tight text-white">
                  SEO Optimization Complete!
                </h1>
                <p className="text-base text-white/70">
                  Your products are now optimized with viral keywords and enhanced SEO
                </p>
              </div>

              {/* Success Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                <div className="text-center bg-white/5 border border-white/20 rounded-2xl p-6">
                  <div className="text-4xl font-bold mb-2 text-white">
                    {completionResults.successCount}
                  </div>
                  <div className="text-sm font-semibold mb-1 text-white">
                    Products Optimized
                  </div>
                  <div className="text-xs text-white/70">
                    Successfully enhanced
                  </div>
                </div>

                <div className="text-center bg-white/5 border border-white/20 rounded-2xl p-6">
                  <div className="text-4xl font-bold mb-2 text-white">
                    {Math.round((completionResults.processingTime || 0) / 1000)}s
                  </div>
                  <div className="text-sm font-semibold mb-1 text-white">
                    Processing Time
                  </div>
                  <div className="text-xs text-white/70">
                    Lightning fast
                  </div>
                </div>

                <div className="text-center bg-white/5 border border-white/20 rounded-2xl p-6">
                  <div className="text-4xl font-bold mb-2 text-white">
                    {completionResults.summary?.averageImprovementScore || 85}%
                  </div>
                  <div className="text-sm font-semibold mb-1 text-white">
                    SEO Score
                  </div>
                  <div className="text-xs text-white/70">
                    Average improvement
                  </div>
                </div>
              </div>

              {/* Viral Keywords Showcase */}
              {completionResults.summary?.topViralKeywords?.length > 0 && (
                <div className="mb-6 bg-white/5 border border-white/20 rounded-2xl p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-6 h-6 bg-white rounded flex items-center justify-center text-xs text-black font-medium">
                      #
                    </div>
                    <div className="text-base font-semibold text-white">
                      Top Viral Keywords Added:
                    </div>
                  </div>
                  <div className="flex gap-2 flex-wrap">
                    {completionResults.summary.topViralKeywords.slice(0, 5).map((keyword: string, index: number) => (
                      <Badge key={index} variant={"secondary" as const} className="text-xs bg-white/20 text-white border-white/30">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Success Message */}
              <div className="mb-6 bg-white/5 border border-white/20 rounded-2xl p-6 text-center">
                <div className="text-lg font-semibold mb-2 text-white">
                  🚀 Your Products Are Now SEO Optimized!
                </div>
                <div className="text-sm text-white/70 leading-relaxed mb-3">
                  Each product now has optimized titles, descriptions, and viral keywords designed to boost your search rankings and drive more traffic to your store.
                </div>
                <div className="text-xs text-white/70 italic">
                  💡 Changes have been automatically applied to your Shopify store
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 justify-center flex-wrap">
                <ShadcnButton
                  onClick={() => window.location.reload()}
                  size="lg"
                  className="bg-white text-black hover:bg-gray-100 font-bold rounded-2xl"
                >
                  Optimize More Products
                </ShadcnButton>
                <ShadcnButton
                  onClick={() => setShowResults(false)}
                  size="lg"
                  className="border border-white/20 bg-white/10 text-white hover:bg-white/20 rounded-2xl"
                >
                  Continue Optimizing
                </ShadcnButton>
              </div>
            </div>
          </div>
        </div>
        </div>
     
    ) : null}

    {/* Pay-Per-Use Confirmation Modal */}
      {showPayPerUseModal && (
        <PayPerUseConfirmation
          productCount={selectedProducts.length}
          selectedProducts={selectedProducts}
          onConfirm={handlePayPerUseConfirm}
          onCancel={handlePayPerUseCancel}
          isProcessing={payPerUseBilling.isProcessing}
          csrfToken={csrfToken}
        />
      )}
    </motion.div>
  );
}
