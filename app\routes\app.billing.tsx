
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { withBulletproofAuth, withBulletproofAction } from "../utils/bulletproof-auth.server";
import { BillingService, SubscriptionData } from "../services/billing.server";
import { ModernBillingDashboard } from "../components/billing/ModernBillingDashboard";
import { CreditsService } from "../services/credits.server";
import { addCSRFToken } from "../utils/csrf.server";
import { handleRouteError, createError, logError } from "../utils/error-handling.server";
import db from "../db.server";

export const loader = withBulletproofAuth(async ({ request, auth }) => {
  const { admin, session } = auth;
  try {
    console.log('Billing loader - Request URL:', request.url);
    console.log('✅ Billing loader - Authenticated for shop:', session.shop);

    // Check for success/error parameters from payment callbacks
    const url = new URL(request.url);
    const success = url.searchParams.get('success');
    const error = url.searchParams.get('error');
    const cancelled = url.searchParams.get('cancelled');

    // Invalidate cache if this is a callback from billing flow or if forced
    const forceRefresh = url.searchParams.get('refresh');
    if (success || cancelled || forceRefresh) {
      console.log(`🔄 Billing callback detected (${success || cancelled || 'force refresh'}), invalidating cache`);
      const { invalidateBillingCache } = await import("../utils/cache.server");
      invalidateBillingCache(session.shop);

      // Also clear any cached subscription data
      invalidateBillingCache(session.shop, 'subscription');
      invalidateBillingCache(session.shop, 'purchase');
    }

    const billingService = new BillingService(admin, session.shop);

    // Get current subscription status
    let billingStatus = await billingService.hasActiveBilling();

    // Auto-sync mechanism: If no active billing found but we have recent database subscription records,
    // force a fresh sync with Shopify to catch any missed subscription updates
    if (!billingStatus.hasAccess || billingStatus.plan?.id === 'pay_per_use') {
      console.log(`🔍 No active billing detected, checking for recent subscription activity...`);

      // Check if we have recent subscription records in database
      const recentSubscription = await db.billingSubscription.findFirst({
        where: {
          shop: session.shop,
          status: { in: ['ACTIVE', 'PENDING'] },
          createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        },
        orderBy: { createdAt: 'desc' }
      });

      if (recentSubscription) {
        console.log(`🔄 Found recent subscription in database, forcing fresh sync from Shopify...`);

        // Clear cache and force fresh data
        const { invalidateBillingCache } = await import("../utils/cache.server");
        invalidateBillingCache(session.shop);

        // Get fresh subscription data from Shopify
        const subscriptionData = await billingService.getCurrentSubscription();
        const allSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];

        console.log(`📊 Fresh Shopify data: ${allSubscriptions.length} subscriptions found`);

        if (allSubscriptions.length > 0) {
          const subscription = allSubscriptions[0];
          console.log(`📋 Syncing subscription: ${subscription.id}, status: ${subscription.status}`);

          // Helper function to determine plan ID
          const determinePlanId = (sub: any): string => {
            if (!sub.name) return 'monthly';
            const name = sub.name.toLowerCase();
            if (name.includes('annual') || name.includes('yearly')) return 'annual';
            return 'monthly';
          };

          // Update database with fresh subscription data
          await db.$transaction(async (tx) => {
            // Update billing subscription
            await tx.billingSubscription.upsert({
              where: { subscriptionId: subscription.id },
              update: {
                status: subscription.status,
                planId: determinePlanId(subscription),
                trialDays: subscription.trialDays || 0,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                updatedAt: new Date()
              },
              create: {
                shop: session.shop,
                subscriptionId: subscription.id,
                status: subscription.status,
                planId: determinePlanId(subscription),
                trialDays: subscription.trialDays || 0,
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                priceAmount: determinePlanId(subscription) === 'annual' ? 299.99 : 29.99,
                priceCurrency: 'USD'
              }
            });

            // Update session
            await tx.session.updateMany({
              where: { shop: session.shop },
              data: {
                subscriptionId: subscription.id,
                subscriptionStatus: subscription.status,
                billingPlanId: determinePlanId(subscription),
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
                lastBillingCheck: new Date()
              }
            });
          });

          console.log(`✅ Auto-sync completed, refreshing billing status...`);

          // Clear cache again and get fresh billing status
          invalidateBillingCache(session.shop);
          billingStatus = await billingService.hasActiveBilling();

          console.log(`🎯 Updated billing status: hasAccess=${billingStatus.hasAccess}, plan=${billingStatus.plan?.name}`);
        }
      }
    }

    const oneTimePurchases = await billingService.getOneTimePurchases();
    const plans = billingService.getAllBillingPlans();

    // Get credit data
    const creditsService = new CreditsService(session.shop);
    const creditBalance = await creditsService.getCreditBalance();
    const creditHistory = await creditsService.getCreditHistory();
  
  // Calculate monthly usage (mock data for now - would come from database)
  const monthlyUsage = {
    productsOptimized: 45,
    totalSpent: billingStatus.plan?.type === 'pay_per_use' ? 4.50 : (billingStatus.plan?.price || 0),
    lastOptimization: new Date().toISOString()
  };

  // Format recent purchases
  const recentPurchases = oneTimePurchases.data?.currentAppInstallation?.oneTimePurchases?.edges?.map((edge: any) => ({
    id: edge.node.id,
    amount: parseFloat(edge.node.price.amount),
    productCount: Math.round(parseFloat(edge.node.price.amount) / 0.10), // Calculate from amount
    date: edge.node.createdAt,
    status: edge.node.status
  })) || [];

  const data = {
    subscription: billingStatus.subscription,
    plan: billingStatus.plan,
    hasAccess: billingStatus.hasAccess,
    monthlyUsage,
    recentPurchases,
    plans,
    creditBalance,
    creditHistory,
    success,
    error,
    shop: session.shop
  };

  // Add CSRF token for security
  return addCSRFToken(session.shop, data);
  } catch (error) {
    console.error('❌ Billing loader error:', error);

    // Log the error with context
    await logError(
      error instanceof Error ? error : new Error(String(error)),
      { shop: session?.shop || 'unknown', action: 'billing_loader' }
    );

    // Return fallback data to prevent UI crashes
    return {
      subscription: undefined,
      plan: undefined,
      hasAccess: false,
      monthlyUsage: {
        productsOptimized: 0,
        totalSpent: 0,
        lastOptimization: new Date().toISOString()
      },
      recentPurchases: [],
      plans: [],
      shop: session?.shop || '',
      error: error instanceof Error ? error.message : "Failed to load billing information",
      errorCode: 'BILLING_DATA_LOAD_FAILED'
    };
  }
});

export const action = withBulletproofAction(async (args) => {
  const { handleBillingAction } = await import("../utils/billing-actions.server");
  return handleBillingAction(args);
});

export default function BillingPage() {
  const {
    subscription,
    plan,
    monthlyUsage,
    recentPurchases,
    plans,
    creditBalance,
    creditHistory,
    success,
    error,
    csrfToken
  } = useLoaderData<typeof loader>();

  return (
    <>
      <TitleBar title="ProdRankX Billing" />

      {/* Success/Error Messages - BLACK THEME WITH GLASS GREY */}
      {success && (
        <div className="mx-6 mb-6">
          <div className="bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm shadow-2xl">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-white font-bold text-lg">
                  {success === 'subscription' ? 'Payment Successful!' : 'Payment Successful!'}
                </h3>
                <p className="text-white/70 text-sm">
                  Your payment has been processed and credits have been added to your account.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-xs text-white/70">
              <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
              <span>Ready to start optimizing your products</span>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="mx-6 mb-6">
          <div className="bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div>
                <h3 className="text-white font-bold text-lg">Payment Error</h3>
                <p className="text-white/70 text-sm">
                  There was an issue processing your payment. Please try again or contact support.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-xs text-white/70">
              <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
              <span>Please check your payment details and try again</span>
            </div>
          </div>
        </div>
      )}

      {/* Modern Black Hero Section */}
      <div className="min-h-screen bg-black text-white">
        {/* Header Section */}
        <div className="bg-black py-20 px-6">
          <div className="max-w-6xl mx-auto text-center">
            {/* Logo and Title */}
            <div className="flex flex-col items-center mb-12">
              <img
                src="/logo.png"
                alt="ProdRankX Logo"
                className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
                style={{ filter: 'brightness(1.1) contrast(1.1)' }}
              />
              <h1 style={{
                fontSize: 'clamp(3rem, 8vw, 6rem)',
                fontWeight: 900,
                lineHeight: 0.9,
                letterSpacing: '-0.05em',
                marginBottom: '1rem',
                color: 'white'
              }}>
                BILLING
              </h1>
              <p style={{
                fontSize: 'clamp(1.25rem, 3vw, 1.75rem)',
                fontWeight: 300,
                color: '#cbd5e1',
                maxWidth: '40rem',
                margin: '0 auto'
              }}>
                Manage your ProdRankX subscription and usage
              </p>
            </div>
          </div>
        </div>

        {/* Billing Content */}
        <div className="px-6 pb-20">
          <ModernBillingDashboard
            subscription={subscription as SubscriptionData | undefined}
            plan={plan}
            monthlyUsage={monthlyUsage}
            recentPurchases={recentPurchases}
            plans={plans}
            creditBalance={creditBalance}
            creditHistory={creditHistory}
            csrfToken={csrfToken}
          />
        </div>
      </div>
    </>
  );
}
