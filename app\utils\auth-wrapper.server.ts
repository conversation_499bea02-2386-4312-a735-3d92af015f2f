/**
 * Enhanced Authentication Wrapper
 * Provides robust authentication handling with better error recovery
 */

import { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export interface AuthenticatedRequest {
  admin: any;
  session: {
    id: string;
    shop: string;
    accessToken: string;
    expires?: Date;
    userId?: string;
    email?: string;
    isOnline: boolean;
    scope?: string;
  };
}

/**
 * Ultra-robust authentication wrapper with comprehensive error handling
 */
export async function authenticateWithRetry(
  request: Request,
  maxRetries: number = 3
): Promise<AuthenticatedRequest> {
  let lastError: any;
  const url = new URL(request.url);
  const shop = url.searchParams.get('shop') || extractShopFromHeaders(request);

  console.log(`🔐 [ULTRA-AUTH] Starting authentication for: ${shop || 'unknown'}`);

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        console.log(`🔄 [ULTRA-AUTH] Retry attempt ${attempt}/${maxRetries} for shop: ${shop}`);
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 200));
      }

      // Try authentication
      const result = await authenticate.admin(request);

      if (attempt > 0) {
        console.log(`✅ [ULTRA-AUTH] Authentication succeeded on retry ${attempt} for shop: ${result.session.shop}`);
      } else {
        console.log(`✅ [ULTRA-AUTH] Authentication succeeded for shop: ${result.session.shop}`);
      }

      // Comprehensive validation
      if (!result.session?.shop || !result.session?.accessToken) {
        throw new Error('Invalid session data received - missing shop or accessToken');
      }

      if (!result.admin) {
        throw new Error('Invalid session data received - missing admin client');
      }

      // Additional session health check
      try {
        // Quick test to ensure the admin client works
        await result.admin.rest.resources.Shop.all({ session: result.session });
        console.log(`✅ [ULTRA-AUTH] Admin client validated for shop: ${result.session.shop}`);
      } catch (adminError) {
        console.warn(`⚠️ [ULTRA-AUTH] Admin client test failed, but continuing: ${adminError}`);
        // Don't fail here, just log the warning
      }

      return result;
    } catch (error: any) {
      lastError = error;
      console.error(`❌ [ULTRA-AUTH] Attempt ${attempt + 1} failed for shop: ${shop}`, {
        message: error.message,
        status: error.status,
        headers: error.headers,
        isResponse: error instanceof Response
      });

      // Handle specific error types
      if (error instanceof Response) {
        const status = error.status;
        const location = error.headers.get('location');

        console.log(`🔍 [ULTRA-AUTH] Response error - Status: ${status}, Location: ${location}`);

        // If it's a redirect to auth, don't retry
        if (status === 302 && location?.includes('/auth')) {
          console.log(`🚫 [ULTRA-AUTH] Auth redirect detected, stopping retries`);
          break;
        }
      }

      // Don't retry on certain errors
      if (error.message?.includes('redirect') ||
          error.status === 302 ||
          error.message?.includes('unauthorized') ||
          attempt === maxRetries) {
        console.log(`🚫 [ULTRA-AUTH] Stopping retries due to error type or max attempts reached`);
        break;
      }
    }
  }

  console.error(`❌ [ULTRA-AUTH] All authentication attempts failed for shop: ${shop}`);
  throw lastError;
}

/**
 * Extract shop from request headers or URL
 */
function extractShopFromHeaders(request: Request): string | null {
  // Try to get shop from various sources
  const shopHeader = request.headers.get('x-shopify-shop-domain');
  if (shopHeader) return shopHeader;

  const referer = request.headers.get('referer');
  if (referer) {
    const match = referer.match(/shop=([^&]+)/);
    if (match) return match[1];
  }

  return null;
}

/**
 * Ultra-safe loader wrapper with comprehensive authentication handling
 */
export function withAuth<T>(
  loaderFn: (args: LoaderFunctionArgs & { auth: AuthenticatedRequest }) => Promise<T>
) {
  return async (args: LoaderFunctionArgs): Promise<T> => {
    const url = new URL(args.request.url);
    const pathname = url.pathname;

    try {
      console.log(`🔐 [WITH-AUTH] Authenticating request to: ${pathname}`);

      const auth = await authenticateWithRetry(args.request);

      console.log(`✅ [WITH-AUTH] Authentication successful for: ${pathname}, shop: ${auth.session.shop}`);

      return await loaderFn({ ...args, auth });
    } catch (error: any) {
      console.error(`❌ [WITH-AUTH] Authentication failed for: ${pathname}`, {
        message: error.message,
        status: error.status,
        isResponse: error instanceof Response
      });

      // If it's already a Response (redirect), let it through
      if (error instanceof Response) {
        console.log(`🔄 [WITH-AUTH] Passing through Response redirect for: ${pathname}`);
        throw error;
      }

      // Create a proper auth redirect
      console.log(`🔄 [WITH-AUTH] Creating auth redirect for: ${pathname}`);

      // Preserve the original URL for after auth
      const redirectUrl = `/auth/login?return_to=${encodeURIComponent(pathname + url.search)}`;

      throw new Response(null, {
        status: 302,
        headers: {
          Location: redirectUrl,
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }
  };
}

/**
 * Safe action wrapper with authentication
 */
export function withAuthAction<T>(
  actionFn: (args: ActionFunctionArgs & { auth: AuthenticatedRequest }) => Promise<T>
) {
  return async (args: ActionFunctionArgs): Promise<T> => {
    try {
      const auth = await authenticateWithRetry(args.request);
      return await actionFn({ ...args, auth });
    } catch (error: any) {
      console.error('❌ Authentication error in action:', error);
      
      // If it's a redirect response, let it through
      if (error instanceof Response) {
        throw error;
      }
      
      // For other errors, create a proper redirect
      throw new Response(null, {
        status: 302,
        headers: {
          Location: '/auth/login'
        }
      });
    }
  };
}
