/**
 * Simple Billing Success Route - NO AUTHENTICATION REQUIRED
 * This route handles Shopify billing callbacks without authentication issues
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type'); // 'subscription' or 'purchase'
    const shop = url.searchParams.get('shop');
    
    console.log(`🎉 [BILLING-SUCCESS] Callback received - Type: ${type}, Shop: ${shop}`);
    console.log(`🔗 [BILLING-SUCCESS] Full URL: ${request.url}`);
    
    if (!type || !shop) {
      console.error('❌ [BILLING-SUCCESS] Missing type or shop parameter');
      return redirect('/app/billing?error=missing_params');
    }
    
    // Redirect to Shopify admin with the app embedded to re-establish session
    const successType = type === 'subscription' ? 'subscription_created' : 'purchase_created';

    // Use Shopify's admin URL format to launch the embedded app with proper session
    const redirectUrl = `https://${shop}/admin/apps/AI BULK SEO/app/billing?success=${successType}&embedded=1`;

    console.log(`✅ [BILLING-SUCCESS] Redirecting to Shopify admin: ${redirectUrl}`);

    return redirect(redirectUrl);
    
  } catch (error) {
    console.error('❌ [BILLING-SUCCESS] Error:', error);
    return redirect('/app/billing?error=callback_failed');
  }
};

// This route doesn't need a component since it always redirects
export default function BillingSuccess() {
  return (
    <div>
      <h1>Processing payment...</h1>
      <p>Please wait while we redirect you back to the app.</p>
    </div>
  );
}
