import { PrismaClient } from "@prisma/client";

declare global {
  var prismaGlobal: PrismaClient | undefined;
}

// Create a single Prisma client instance
let prisma: PrismaClient;

if (process.env.NODE_ENV === "production") {
  // Production: Create new instance
  console.log('🚀 Initializing production database with PostgreSQL');
  prisma = new PrismaClient({
    log: ['error'],
    errorFormat: 'pretty',
  });
} else {
  // Development: Use global instance to prevent multiple connections
  console.log('🔧 Development mode - using PostgreSQL');
  if (!global.prismaGlobal) {
    global.prismaGlobal = new PrismaClient({
      log: ['query', 'error', 'warn'],
      errorFormat: 'pretty',
    });
  }
  prisma = global.prismaGlobal;
}

// Initialize connection
prisma.$connect().catch((error: any) => {
  console.error('❌ Failed to initialize database connection:', error);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

export default prisma;
