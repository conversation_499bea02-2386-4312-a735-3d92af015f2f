import { useState, useC<PERSON>back, useEffect } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { requireAuth } from "../utils/bulletproof-auth.server";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export const loader = requireAuth(async () => {
  return json({});
});

export const action = requireAuth(async () => {
  return json({ success: true, message: "Settings saved!" });
});

export default function Settings() {
  const fetcher = useFetcher<typeof action>();
  const shopify = useAppBridge();

  const [titlePrompt, setTitlePrompt] = useState(
    "Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"
  );
  const [descriptionPrompt, setDescriptionPrompt] = useState(
    "Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}"
  );

  const isLoading = fetcher.state === "submitting";

  useEffect(() => {
    if (fetcher.data && 'success' in fetcher.data && fetcher.data.success) {
      shopify.toast.show(fetcher.data.message || 'Settings saved!');
    } else if (fetcher.data && 'error' in fetcher.data && fetcher.data.error) {
      shopify.toast.show(fetcher.data.error as string, { isError: true });
    }
  }, [fetcher.data, shopify]);

  // Load saved settings from sessionStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTitlePrompt = sessionStorage.getItem('title_prompt_template');
      const savedDescriptionPrompt = sessionStorage.getItem('description_prompt_template');

      if (savedTitlePrompt) setTitlePrompt(savedTitlePrompt);
      if (savedDescriptionPrompt) setDescriptionPrompt(savedDescriptionPrompt);
    }
  }, []);

  const handleTitlePromptChange = useCallback((value: string) => {
    setTitlePrompt(value);
  }, []);

  const handleDescriptionPromptChange = useCallback((value: string) => {
    setDescriptionPrompt(value);
  }, []);

  const handleSave = useCallback(() => {
    // Store in sessionStorage since we don't have a database
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('title_prompt_template', titlePrompt);
      sessionStorage.setItem('description_prompt_template', descriptionPrompt);
    }

    const formData = new FormData();
    formData.append("action", "save");
    fetcher.submit(formData, { method: "POST" });
  }, [titlePrompt, descriptionPrompt, fetcher]);

  const handleReset = useCallback(() => {
    setTitlePrompt(
      "Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"
    );
    setDescriptionPrompt(
      "Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}"
    );
  }, []);

  return (
    <>
      <TitleBar title="ProdRankX Settings" />

      {/* Modern Black Hero Section */}
      <div className="min-h-screen bg-black text-white">
        {/* Header Section */}
        <div className="bg-black py-20 px-6">
          <div className="max-w-6xl mx-auto text-center">
            {/* Logo and Title */}
            <div className="flex flex-col items-center mb-12">
              <img
                src="/logo.png"
                alt="ProdRankX Logo"
                className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
                style={{ filter: 'brightness(1.1) contrast(1.1)' }}
              />
              <h1 style={{
                fontSize: 'clamp(3rem, 8vw, 6rem)',
                fontWeight: 900,
                lineHeight: 0.9,
                letterSpacing: '-0.05em',
                marginBottom: '1rem',
                color: 'white'
              }}>
                SETTINGS
              </h1>
              <p style={{
                fontSize: 'clamp(1.25rem, 3vw, 1.75rem)',
                fontWeight: 300,
                color: '#cbd5e1',
                maxWidth: '40rem',
                margin: '0 auto'
              }}>
                Configure your ProdRankX optimization preferences
              </p>
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="px-6 pb-20">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* API Configuration */}
            <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-2">API Configuration</h2>
                <p className="text-white/70">Configure your API keys for enhanced functionality</p>
              </div>

              <div className="space-y-6">
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">Google PageSpeed API Key</label>
                  <Input
                    type="password"
                    placeholder="Enter your Google PageSpeed API key"
                    className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                  />
                  <p className="text-xs text-white/60">
                    Required for detailed Core Web Vitals analysis
                  </p>
                </div>

                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">OpenAI API Key</label>
                  <Input
                    type="password"
                    placeholder="Enter your OpenAI API key"
                    className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                  />
                  <p className="text-xs text-white/60">
                    Required for advanced content optimization
                  </p>
                </div>
              </div>
            </div>

            {/* SEO Prompt Templates */}
            <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-2">SEO Prompt Templates</h2>
                <p className="text-white/70">Customize AI prompts for product optimization</p>
              </div>

              <div className="space-y-6">
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">Title Generation Prompt</label>
                  <textarea
                    value={titlePrompt}
                    onChange={(e) => setTitlePrompt(e.target.value)}
                    className="w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none"
                    placeholder="Enter your title generation prompt..."
                    style={{ lineHeight: 1.6 }}
                  />
                </div>

                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">Description Generation Prompt</label>
                  <textarea
                    value={descriptionPrompt}
                    onChange={(e) => setDescriptionPrompt(e.target.value)}
                    className="w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none"
                    placeholder="Enter your description generation prompt..."
                    style={{ lineHeight: 1.6 }}
                  />
                </div>
              </div>
            </div>

            {/* Automation Settings */}
            <div className="bg-white/10 border border-white/20 rounded-3xl p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-2">Automation Settings</h2>
                <p className="text-white/70">Configure bulk processing and automation preferences</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">Batch Size</label>
                  <Input
                    type="number"
                    defaultValue="10"
                    min="1"
                    max="50"
                    className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                  />
                  <p className="text-xs text-white/60">
                    Number of products to process simultaneously
                  </p>
                </div>

                <div className="space-y-3">
                  <label className="text-sm font-semibold text-white">Processing Delay</label>
                  <Input
                    type="number"
                    defaultValue="1000"
                    min="500"
                    max="5000"
                    className="w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                  />
                  <p className="text-xs text-white/60">
                    Delay between requests (milliseconds)
                  </p>
                </div>
              </div>
            </div>

            {/* Save Settings */}
            <div className="flex justify-center pt-8">
              <Button
                onClick={handleSave}
                disabled={isLoading}
                size="lg"
                className="bg-white text-black hover:bg-gray-100 font-bold py-4 px-12 text-lg rounded-2xl transition-colors duration-200"
              >
                {isLoading ? 'Saving Settings...' : 'Save Settings'}
              </Button>
            </div>

            {/* Success Message */}
            {fetcher.data?.success && (
              <div className="mt-8">
                <div className="bg-white/10 border border-white/20 rounded-2xl p-6 text-center">
                  <div className="text-white font-semibold text-lg">
                    ✅ Settings saved successfully!
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
