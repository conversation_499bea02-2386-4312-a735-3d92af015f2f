import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  
  return new Response(
    `<!DOCTYPE html>
    <html>
      <head>
        <script src="https://unpkg.com/@shopify/app-bridge@3"></script>
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            if (window.top !== window.self) {
              window.parent.location.href = window.location.href;
            }
          });
        </script>
      </head>
      <body>
        <p>Redirecting...</p>
      </body>
    </html>`,
    {
      status: 200,
      headers: {
        "Content-Type": "text/html",
      },
    }
  );
};
